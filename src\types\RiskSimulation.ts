import type { TickerRealtimeData } from "./pledgedProject";

export interface SimulateResult {
  time: string;
  status: string;
  message: string;
  LTV: string;
  action: string;
  line: string;
}

export interface SimulationResults extends TickerRealtimeData {
  simulateResult: SimulateResult;
}

export interface SimulationData {
  stock_name: string;
  stock_code: string;
  stock_price: number;
  total_borrowed: number;
  required_shares: number;
  pledge_rate: number;
  warning_line: number;
  alert_line: number;
  liquidation_line: number;
  start_date: string;
  end_date: string;
  warninglineconfig: number;
  alertlineconfig: number;
  liquidationlineconfig: number;
}
