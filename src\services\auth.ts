import { LOGIN_TOKEN } from "../constant/login";
import type { UserInfo } from "../types/User";
import { del, get, post, put } from "../utils/request";

// 模拟登录 API
export const login = async (
  username: string,
  password: string
): Promise<{ token: string; userInfo: UserInfo }> => {
  return post("/user/login", { username, password });
};

// 登出
export const logout = () => {
  localStorage.removeItem(LOGIN_TOKEN);
};

// 获取当前用户信息
export const getCurrentUser = async (): Promise<UserInfo> => {
  return get("/user/getUserInfo");
};

export const changePassword = async (newpassword: string) => {
  return post("/user/changePassword", { newpassword });
};

export const getUserList = async () => {
  return get<UserInfo[]>("/user/getUserList");
};

export const updatePhone = async (phone: string) => {
  return post("/user/updatePhone", { phone });
};

export const createUser = async (user: any) => {
  return post("/user/createUser", user);
};

export const updateUser = async (id: string, user: any) => {
  // return put(`/user/${id}`, user);
  return post(`/user/updateSubordinateUser`, { user_id: id, ...user });
};

export const deleteUser = async (id: string) => {
  // return del(`/user/${id}`);
  return post(`/user/deleteUser`, { user_id: id });
};

export const updateEmail = async (email: string) => {
  return post(`/user/updateEmail`, { email });
};

export const checkPassword = async (password: string) => {
  return post(`/user/checkPassword`, { password });
};
