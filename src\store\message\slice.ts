import { createSlice, type PayloadAction } from "@reduxjs/toolkit";
import type { MessageCentreData } from "../../types/MessageCentre";
interface MessageState {
  messageDetail: MessageCentreData | null;
  loading: boolean;
}

const initialState: MessageState = {
  messageDetail: localStorage.getItem("messageDetail")
    ? JSON.parse(localStorage.getItem("messageDetail")!)
    : null,
  loading: false,
};

export const messageSlice = createSlice({
  name: "message",
  initialState,
  reducers: {
    setMessageDetail: (state, action: PayloadAction<MessageCentreData>) => {
      state.messageDetail = action.payload;
      localStorage.setItem("messageDetail", JSON.stringify(action.payload));
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    clearMessageDetail: (state) => {
      state.messageDetail = null;
      localStorage.removeItem("messageDetail");
    },
  },
});

export const { setMessageDetail, setLoading, clearMessageDetail } =
  messageSlice.actions;

export default messageSlice.reducer;
