import { type ThemeConfig } from "antd";

export const lightTheme: ThemeConfig = {
  token: {
    colorPrimary: "#1677ff",
    borderRadius: 4,
  },
  components: {
    Layout: {
      siderBg: "#001529",
      headerBg: "#ffffff",
    },
  },
};

export const darkTheme: ThemeConfig = {
  token: {
    colorPrimary: "#1677ff",
    colorBgBase: "#0C111D",
    colorTextBase: "rgba(255, 255, 255, 0.85)",
    borderRadius: 4,
    colorBgContainer: "#0C111D",
  },
  components: {
    Layout: {
      siderBg: "#001529",
      headerBg: "#0C111D",
    },
    Modal: {
      contentBg: "#181b23",
      headerBg: "#181b23",
      titleColor: "rgba(255, 255, 255, 0.85)",
      colorIcon: "rgba(255, 255, 255, 0.6)",
      colorIconHover: "rgba(255, 255, 255, 0.8)",
      footerBg: "#181b23",
    },
    Button: {
      defaultBg: "#1d2536",
      defaultColor: "rgba(255, 255, 255, 0.85)",
      defaultBorderColor: "#2e3242",
    },
  },
};
