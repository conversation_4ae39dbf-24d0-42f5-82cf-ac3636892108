import React from "react";
import { useAppSelector } from "../../hooks/useStore";
import type { UserRoleType } from "../../types/User";
import usePermission from "../../hooks/usePermission";

interface AuthGuardProps {
  allowedRoles: UserRoleType | UserRoleType[];
  children: React.ReactNode;
  noPermissionNode?: React.ReactNode;
}

const AuthGuard: React.FC<AuthGuardProps> = ({
  allowedRoles,
  children,
  noPermissionNode,
}) => {
  const { hasPermission } = usePermission();

  if (!hasPermission(allowedRoles)) {
    return noPermissionNode || null;
  }

  return <>{children}</>;
};

export default AuthGuard;
