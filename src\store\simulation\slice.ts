import { createSlice, type PayloadAction } from "@reduxjs/toolkit";
import type {
  SimulationData,
  SimulationResults,
} from "../../types/RiskSimulation";
interface SimulationState {
  simulationResult: SimulationResults[] | null;
  simulationData: SimulationData | null;
  loading: boolean;
}

const initialState: SimulationState = {
  simulationResult: null,
  simulationData: null,
  loading: false,
};

export const simulationSlice = createSlice({
  name: "simulation",
  initialState,
  reducers: {
    setSimulationData: (state, action: PayloadAction<SimulationData>) => {
      state.simulationData = action.payload;
    },
    setSimulationResult: (
      state,
      action: PayloadAction<SimulationResults[]>
    ) => {
      state.simulationResult = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    clearSimulationResult: (state) => {
      state.simulationResult = null;
    },
  },
});

export const {
  setSimulationResult,
  setSimulationData,
  setLoading,
  clearSimulationResult,
} = simulationSlice.actions;

export default simulationSlice.reducer;
