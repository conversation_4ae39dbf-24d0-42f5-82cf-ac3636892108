import React, { useState, useEffect } from "react";
import { Modal, Form, Input, Button, message } from "antd";
import {
  CloseOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import styles from "./index.module.less";
import { changePassword, checkPassword } from "../../services/auth";

interface PasswordModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const PasswordModal: React.FC<PasswordModalProps> = ({
  visible,
  onClose,
  onSuccess,
}) => {
  const { t } = useTranslation();
  const [step, setStep] = useState(1);
  const [form] = Form.useForm();
  const [newPassword, setNewPassword] = useState("");

  useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [visible, step, form]);

  const resetState = () => {
    setStep(1);
    form.resetFields();
    setNewPassword("");
  };

  // 关闭弹窗并重置状态
  const handleClose = () => {
    resetState();
    onClose();
  };

  // 验证当前密码
  const handleCurrentPasswordNext = () => {
    form.validateFields().then(() => {
      checkPassword(form.getFieldValue("currentPassword"))
        .then(() => {
          setStep(2);
        })
        .catch(() => {
          message.error("Password is incorrect");
        });
    });
  };

  // 设置新密码
  const handleNewPasswordNext = () => {
    form.validateFields().then((values) => {
      setNewPassword(values.newPassword);
      setStep(2);
    });
  };

  // 确认并保存新密码
  const handleSavePassword = () => {
    form.validateFields().then((values) => {
      if (values.confirmPassword !== newPassword) {
        form.setFields([
          {
            name: "confirmPassword",
            errors: [t("password.passwordsDoNotMatch")],
          },
        ]);
        return;
      }

      changePassword(newPassword).then(() => {
        handleClose();
        onSuccess();
      });
    });
  };

  // 步骤返回
  const handleBack = () => {
    if (step === 2) {
      setStep(1);
    }
  };

  // 获取当前步骤的表单内容
  const getFormItems = () => {
    switch (step) {
      // case 1:
      //   return (
      //     <Form.Item
      //       label={t("password.yourCurrentPassword")}
      //       name="currentPassword"
      //       rules={[
      //         {
      //           required: true,
      //           message: t("password.pleaseEnterCurrentPassword"),
      //         },
      //       ]}
      //     >
      //       <Input.Password
      //         placeholder={t("password.enterCurrent")}
      //         iconRender={(visible) =>
      //           visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
      //         }
      //       />
      //     </Form.Item>
      //   );
      case 1:
        return (
          <Form.Item
            label={t("password.yourNewPassword")}
            name="newPassword"
            rules={[
              {
                required: true,
                message: t("password.pleaseEnterNewPassword"),
              },
              { min: 6, message: t("password.passwordMinLength") },
            ]}
          >
            <Input.Password
              placeholder={t("password.enterNew")}
              iconRender={(visible) =>
                visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
              }
            />
          </Form.Item>
        );
      case 2:
        return (
          <Form.Item
            label={t("password.retypeNewPassword")}
            name="confirmPassword"
            rules={[
              {
                required: true,
                message: t("password.pleaseConfirmPassword"),
              },
            ]}
          >
            <Input.Password
              placeholder={t("password.confirmNew")}
              iconRender={(visible) =>
                visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
              }
            />
          </Form.Item>
        );
      default:
        return null;
    }
  };

  // 获取当前步骤的按钮
  const getStepButtons = () => {
    switch (step) {
      // case 1:
      //   return (
      //     <>
      //       <Button className={styles.cancelBtn} onClick={handleClose}>
      //         {t("password.cancel")}
      //       </Button>
      //       <Button
      //         type="primary"
      //         className={styles.saveBtn}
      //         onClick={handleCurrentPasswordNext}
      //       >
      //         {t("password.next")}
      //       </Button>
      //     </>
      //   );
      case 1:
        return (
          <>
            {/* <Button className={styles.cancelBtn} onClick={handleBack}>
              {t("password.back")}
            </Button> */}
            <Button
              type="primary"
              className={styles.saveBtn}
              onClick={handleNewPasswordNext}
              style={{ width: "100%" }}
            >
              {t("password.next")}
            </Button>
          </>
        );
      case 2:
        return (
          <>
            <Button className={styles.cancelBtn} onClick={handleBack}>
              {t("password.back")}
            </Button>
            <Button
              type="primary"
              className={styles.saveBtn}
              onClick={handleSavePassword}
            >
              {t("password.saveAndUpdate")}
            </Button>
          </>
        );
      default:
        return null;
    }
  };

  // 获取当前步骤的标题
  const getStepTitle = () => {
    switch (step) {
      // case 1:
      //   return t("password.enterCurrent");
      case 1:
        return t("password.enterNew");
      case 2:
        return t("password.confirmNew");
      default:
        return "";
    }
  };

  return (
    <Modal
      title={getStepTitle()}
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={480}
      closeIcon={<CloseOutlined />}
      className={styles.editModal}
      centered
    >
      <Form form={form} layout="vertical">
        {getFormItems()}
        <div className={styles.modalFooter}>{getStepButtons()}</div>
      </Form>
    </Modal>
  );
};

export default PasswordModal;
