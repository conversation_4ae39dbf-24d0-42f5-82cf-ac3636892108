import React, { useEffect } from "react";
import { Modal, Input, Button, Select, Form } from "antd";
import styles from "./AddUserModal.module.less";
import { UserRole, type UserInfo, type UserRoleType } from "../../types/User";
import { USER_ROLE_MAP } from "../../constant/login";

interface AddUserModalProps {
  visible: boolean;
  user?: UserInfo | null;
  onCancel: () => void;
  onOk: (values: UserFormValues, id?: string) => void;
}

export interface UserFormValues {
  username: string;
  password: string;
  confirmPassword: string;
  role: string;
  real_name: string;
}

const AddUserModal: React.FC<AddUserModalProps> = ({
  visible,
  user,
  onCancel,
  onOk,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible) {
      form.resetFields();
    }
    if (user) {
      form.setFieldsValue({
        ...user,
        real_name: user.real_name === "0" ? "" : user.real_name,
      });
    }
  }, [visible]);
  const handleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        onOk(values, user?.id);
      })
      .catch((info) => {
        console.log("Validation failed:", info);
      });
  };

  return (
    <Modal
      title={user?.id ? "Edit User Account" : "Add User Account"}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={740}
      className={styles.addUserModal}
      maskClosable={false}
      destroyOnClose
    >
      <Form form={form} layout="vertical" name="addUserForm" preserve={false}>
        <Form.Item
          name="username"
          label="Username"
          rules={
            user?.id
              ? []
              : [{ required: true, message: "Please enter username" }]
          }
        >
          <Input placeholder="Enter username here" disabled={!!user?.id} />
        </Form.Item>
        <Form.Item name="real_name" label="Real Name">
          <Input placeholder="Enter real name here" />
        </Form.Item>
        <Form.Item
          name="email"
          label="Email"
          rules={
            user?.id
              ? [{ type: "email", message: "Please enter a valid email" }]
              : [
                  { required: true, message: "Please enter email" },
                  { type: "email", message: "Please enter a valid email" },
                ]
          }
        >
          <Input placeholder="Enter email here" />
        </Form.Item>
        <Form.Item
          name="role"
          label="Assign Role"
          rules={[{ required: true, message: "Please select a role" }]}
        >
          <Select placeholder="Select role" disabled={!!user?.id}>
            {Object.values(UserRole).map((key: UserRoleType) => (
              <Select.Option key={key} value={key}>
                {USER_ROLE_MAP[key]}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item name="phone" label="Phone">
          <Input placeholder="Enter phone here" />
        </Form.Item>
        <Form.Item
          name="password"
          label="Password"
          rules={
            user?.id
              ? [{ min: 6, message: "Password must be at least 6 characters" }]
              : [
                  { required: true, message: "Please enter password" },
                  { min: 6, message: "Password must be at least 6 characters" },
                ]
          }
        >
          <Input.Password placeholder="Enter password here" />
        </Form.Item>
        <Form.Item
          name="confirmPassword"
          label="Confirm Password"
          dependencies={["password"]}
          rules={[
            ...(user?.id
              ? []
              : [{ required: true, message: "Please enter confirm password" }]),
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue("password") === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error("The two passwords do not match")
                );
              },
            }),
          ]}
        >
          <Input.Password placeholder="Enter password here" />
        </Form.Item>
        <div className={styles.modalFooter}>
          <Button className={styles.cancelButton} onClick={onCancel}>
            Cancel
          </Button>
          <Button
            type="primary"
            className={styles.doneButton}
            onClick={handleSubmit}
          >
            Done
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default AddUserModal;
