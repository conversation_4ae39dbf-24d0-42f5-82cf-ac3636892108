export type RiskLevel = "critical" | "high" | "medium" | "low";

export interface PledgedProject {
  alert_line: string;
  alertlineconfig: string;
  change_percent: string;
  current_stock_price: string;
  end_date: string;
  liquidation_line: string;
  liquidationlineconfig: string;
  pledge_price: string;
  // pledge_rate: string; stock_price
  project_id: number;
  relations: Array<{
    relation_id: number;
    project_id: number;
    borrower_name: string;
    lender_name: string;
    borrower_id: number;
    lender_id: number;
    borrowed_amount: string;
  }>;
  required_shares: number;
  safety_status:
    | {
        line: string;
        message: string;
        title: string;
      }
    | string;
  start_date: string;
  stock_code: string;
  stock_name: string;
  stock_price: string;
  stock_update_time: string;
  total_borrowed: string;
  warning_line: string;
  warninglineconfig: string;
  [key: string]: any;
}

export interface RiskOverviewData {
  total_borrowed: number;
  total_projects: number;
  risk_stats: {
    critical: { count: number; percentage: number };
    low: { count: number; percentage: number };
    medium: { count: number; percentage: number };
    high: { count: number; percentage: number };
  };
}

export interface NowStockInfo {}

export interface TickerRealtimeData {
  ticker: string;
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface PledgedProjectLender {
  lender_id: number;
  lender_name: string;
  borrowed_amount: string;
  contribution_percentage: string;
}
