import React from "react";
import { <PERSON>, <PERSON>, But<PERSON> } from "antd";
import { SyncOutlined } from "@ant-design/icons";
import PledgedProjectCard from "../../components/PledgedProjectCard";
import styles from "./style.module.less";
import { useNavigate } from "react-router-dom";
import type { PledgedProject, RiskLevel } from "../../types/pledgedProject";

interface PledgedProjectsProps {
  projects: PledgedProject[];
  onRefresh?: () => void;
}

const PledgedProjects: React.FC<PledgedProjectsProps> = ({
  projects,
  onRefresh,
}) => {
  const navigator = useNavigate();
  const handleCardClick = (id: number) => {
    navigator(`/pledged-detail/${id}`);
  };

  return (
    <div className={styles.pledgedProjectsSection}>
      <div className={styles.sectionHeader}>
        <div className={styles.sectionHeaderTitle}>Pledged Projects</div>
        <Button
          className={styles.refreshButton}
          type="text"
          icon={<SyncOutlined className={styles.refreshButtonIcon} />}
          onClick={onRefresh}
        >
          Refresh
        </Button>
      </div>

      <Row gutter={[16, 16]}>
        {projects.map((project) => (
          <Col
            xs={24}
            sm={12}
            md={12}
            lg={12}
            xl={8}
            xxl={6}
            key={project.project_id}
          >
            <PledgedProjectCard
              id={project.project_id}
              stockCode={project.stock_code}
              stockName={project.stock_name}
              requiredShares={project.required_shares}
              currentPrice={parseFloat(project.current_stock_price)}
              priceChange={project.change_percent}
              pledgedPrice={
                project.pledge_price !== undefined &&
                project.pledge_price !== null &&
                project.pledge_price !== ""
                  ? parseFloat(project.pledge_price)
                  : parseFloat(
                      (
                        Number(project.stock_price) *
                        (Number(project.pledge_rate) / 100)
                      ).toFixed(2)
                    )
              }
              pledgedRatio={parseFloat(project.pledge_rate)}
              warningPrice={parseFloat(project.warning_line)}
              alertPrice={parseFloat(project.alert_line)}
              liquidationPrice={parseFloat(project.liquidation_line)}
              riskLevel={
                typeof project.safety_status === "string"
                  ? (project.safety_status?.toLowerCase() as RiskLevel)
                  : (project.safety_status.line?.toLowerCase() as RiskLevel)
              }
              start_date={project.start_date}
              end_date={project.end_date}
              onClick={() => handleCardClick(project.project_id)}
            />
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default PledgedProjects;
