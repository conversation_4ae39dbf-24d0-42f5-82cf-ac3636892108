import React from "react";
import type { ReactNode } from "react";
import { Button } from "antd";
import { LeftOutlined } from "@ant-design/icons";
import styles from "./style.module.less";
import Icon from "../Icon";

interface PageHeaderProps {
  title?: string;
  onBack?: () => void;
  backText?: string;
  showBackButton?: boolean;
  rightContent?: ReactNode;
}

/**
 * 页面头部组件
 * 包含返回按钮和右侧自定义内容区域
 */
const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  onBack,
  backText = "Back",
  showBackButton = true,
  rightContent,
}) => {
  return (
    <div className={styles.pageHeader}>
      <div className={styles.leftSection}>
        {showBackButton && (
          <Button
            type="text"
            icon={
              <Icon
                name="back"
                size={16}
                color="#94969C"
                style={{ marginTop: 4 }}
              />
            }
            onClick={onBack}
            className={styles.backButton}
          >
            {backText}
          </Button>
        )}
        {title && <div className={styles.title}>{title}</div>}
      </div>
      {rightContent && (
        <div className={styles.rightSection}>{rightContent}</div>
      )}
    </div>
  );
};

export default PageHeader;
