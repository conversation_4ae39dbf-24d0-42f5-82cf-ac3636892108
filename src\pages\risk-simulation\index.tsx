import React, { useEffect, useRef } from "react";
import {
  Typography,
  Input,
  Select,
  Button,
  Form,
  DatePicker,
  Row,
  Col,
} from "antd";
import styles from "./style.module.less";
import RiskSimulationDetail from "./Detail";
import { createSimulation } from "../../services/RiskSimulation";
import { store, type RootState } from "../../store";
import {
  setLoading,
  setSimulationData,
  setSimulationResult,
} from "../../store/simulation/slice";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import useMobile from "../../hooks/useMobile";
import { getTickerRealtimeData } from "../../services/pledgedProjects";
import dayjs from "dayjs";

const { Title } = Typography;

// 模拟股票数据
const stockOptions = [
  { value: "HONG LEONG 5819", label: "HONG LEONG 5819" },
  { value: "CIMB GROUP 1023", label: "CIMB GROUP 1023" },
  { value: "GENTING BHD 3182", label: "GENTING BHD 3182" },
  { value: "DIGI.COM 6947", label: "DIGI.COM 6947" },
  { value: "TOP GLOVE 7113", label: "TOP GLOVE 7113" },
];

const RiskSimulation: React.FC = () => {
  const isMobile = useMobile();

  const navigate = useNavigate();
  const [form] = Form.useForm();
  const { loading: isSimulating } = useSelector(
    (state: RootState) => state.simulation
  );
  const getStockInfoTimer = useRef<any>(null);

  // 表单提交处理
  const handleFormSubmit = (values: any) => {
    store.dispatch(setLoading(true));
    values.start_date = dayjs(values.start_date).format("YYYY-MM-DD");
    values.end_date = dayjs(values.end_date).format("YYYY-MM-DD");
    createSimulation(values)
      .then((res) => {
        store.dispatch(setSimulationResult(res.data));
        store.dispatch(setSimulationData(values));
        if (isMobile) {
          navigate(`/risk-simulation/${res.project_id}`);
        }
      })
      .finally(() => {
        store.dispatch(setLoading(false));
      });
  };

  useEffect(() => {
    return () => {
      if (!isMobile) {
        store.dispatch(setSimulationResult([]));
      }
    };
  }, [isMobile]);

  const getStockPrice = () => {
    const stock_code = form.getFieldValue("stock_code");
    const start_date = form.getFieldValue("start_date");
    if (!stock_code) {
      return;
    }
    getTickerRealtimeData({
      ticker: stock_code,
      ...(start_date
        ? { start_date: dayjs(start_date).format("YYYY-MM-DD") }
        : {}),
    })
      .then((res) => {
        form.setFieldsValue({
          stock_price: res[0].high,
        });
      })
      .finally(() => {});
  };

  const handleStockCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    clearTimeout(getStockInfoTimer.current);
    getStockInfoTimer.current = setTimeout(() => {
      const value = e.target.value;
      if (/^\d+$/.test(value)) {
        getStockPrice();
      }
    }, 500);
  };

  const handleStartDateChange = (date: any) => {
    getStockPrice();
  };

  const handleFormValuesChange = (changedValues: any, allValues: any) => {
    let warninglineconfig = parseFloat(allValues.warninglineconfig);
    let alertlineconfig = parseFloat(allValues.alertlineconfig);
    let liquidationlineconfig = parseFloat(allValues.liquidationlineconfig);

    let initialPrice = parseFloat(allValues.stock_price);

    const isChangePledgeRatio =
      Object.keys(changedValues).includes("pledge_rate");
    const isChangeTotalBorrowed =
      Object.keys(changedValues).includes("total_borrowed");
    const isChangeRequiredShares =
      Object.keys(changedValues).includes("required_shares");
    const pledgeRatio = isChangePledgeRatio
      ? parseFloat(changedValues.pledge_rate) / 100
      : parseFloat(allValues.pledge_rate) / 100;
    const totalBorrowed = isChangeTotalBorrowed
      ? changedValues.total_borrowed
      : allValues.total_borrowed;

    let requiredShares = isChangeRequiredShares
      ? changedValues.required_shares
      : allValues.required_shares;

    if (isChangeTotalBorrowed || isChangeRequiredShares) {
      if (isChangeTotalBorrowed) {
        // 如果修改借款金额，根据股票数量和质押率，推导出requiredShares
        requiredShares = totalBorrowed / (initialPrice * pledgeRatio);
        if (!isNaN(requiredShares)) {
          requiredShares = Number(requiredShares.toFixed(2));
          form.setFieldsValue({
            required_shares: requiredShares,
          });
        }
      }
      if (isChangeRequiredShares) {
        // 如果修改股票数量，根据借款金额 和质押率，推导出initialPrice
        initialPrice =
          allValues.total_borrowed /
          (changedValues.required_shares * pledgeRatio);
        if (!isNaN(initialPrice)) {
          initialPrice = Number(initialPrice.toFixed(2));
          form.setFieldsValue({
            stock_price: initialPrice,
          });
        }
      }
    } else {
      requiredShares = totalBorrowed / (initialPrice * pledgeRatio);
      if (!isNaN(requiredShares)) {
        requiredShares = Number(requiredShares.toFixed(2));
        form.setFieldsValue({
          required_shares: requiredShares,
        });
      }
    }
    if (pledgeRatio > 0.8) {
      warninglineconfig = 1.2;
      alertlineconfig = 1.15;
      liquidationlineconfig = 1.1;
    } else if (pledgeRatio > 0.7 && pledgeRatio <= 0.8) {
      warninglineconfig = 1.25;
      alertlineconfig = 1.17;
      liquidationlineconfig = 1.1;
    } else {
      warninglineconfig = 1.4;
      alertlineconfig = 1.2;
      liquidationlineconfig = 1.15;
    }

    if (Object.keys(changedValues).includes("warninglineconfig")) {
      warninglineconfig = parseFloat(allValues.warninglineconfig);
      if (warninglineconfig < 1) {
        form.setFieldsValue({
          warninglineconfig: 1,
        });
      }
    } else {
      form.setFieldsValue({
        warninglineconfig: warninglineconfig,
      });
    }
    if (Object.keys(changedValues).includes("alertlineconfig")) {
      alertlineconfig = parseFloat(allValues.alertlineconfig);
      if (alertlineconfig < 1) {
        form.setFieldsValue({
          alertlineconfig: 1,
        });
      }
    } else {
      form.setFieldsValue({
        alertlineconfig: alertlineconfig,
      });
    }
    if (Object.keys(changedValues).includes("liquidationlineconfig")) {
      liquidationlineconfig = parseFloat(allValues.liquidationlineconfig);
      if (liquidationlineconfig < 1) {
        form.setFieldsValue({
          liquidationlineconfig: 1,
        });
      }
    } else {
      form.setFieldsValue({
        liquidationlineconfig: liquidationlineconfig,
      });
    }

    let warningLine = initialPrice * pledgeRatio * warninglineconfig;
    if (!isNaN(warningLine)) {
      warningLine = Number(warningLine.toFixed(2));
      form.setFieldsValue({
        warning_line: warningLine,
      });
    }

    let alertLine = initialPrice * pledgeRatio * alertlineconfig;
    if (!isNaN(alertLine)) {
      alertLine = Number(alertLine.toFixed(2));
      form.setFieldsValue({
        alert_line: alertLine,
      });
    }

    let liquidationLine = initialPrice * pledgeRatio * liquidationlineconfig;
    if (!isNaN(liquidationLine)) {
      liquidationLine = Number(liquidationLine.toFixed(2));
      form.setFieldsValue({
        liquidation_line: liquidationLine,
      });
    }
    let pledgePrice = initialPrice * pledgeRatio;
    if (!isNaN(pledgePrice)) {
      pledgePrice = Number(pledgePrice.toFixed(2));
      form.setFieldsValue({
        pledge_price: pledgePrice,
      });
    }
  };

  return (
    <div className={styles.riskSimulationPage}>
      <Title level={1} className={styles.title}>
        Risk Simulation
      </Title>

      <div className={styles.contentContainer}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
          onValuesChange={handleFormValuesChange}
          className={styles.form}
        >
          {/* 参数卡片 */}
          <div className={styles.parametersCard}>
            <div className={styles.cardTitle}>Parameters</div>
            <Form.Item
              name="stock_name"
              label="Stock Name"
              rules={[{ required: true, message: "Please enter stock name" }]}
            >
              <Input placeholder="eg. IBM" />
            </Form.Item>
            <Form.Item
              name="stock_code"
              label="Stock Code"
              rules={[
                { required: true, message: "Please enter stock code" },
                {
                  validator(_, value) {
                    if (value && !/^\d+$/.test(value)) {
                      return Promise.reject(
                        new Error("Stock code must be a number")
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input placeholder="eg. 0186" onChange={handleStockCodeChange} />
            </Form.Item>
            <Form.Item
              name="stock_price"
              label="Stock Price"
              rules={[{ required: true, message: "Please enter stock price" }]}
            >
              <Input placeholder="eg. Price" />
            </Form.Item>
            <Form.Item
              name="total_borrowed"
              label="Total Borrowed"
              rules={[
                { required: true, message: "Please enter total borrowed" },
              ]}
            >
              <Input placeholder="eg. 10,000" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="pledge_rate"
                  label="Pledge Ratio %"
                  rules={[
                    { required: true, message: "Please enter pledge ratio" },
                  ]}
                >
                  <Input placeholder="eg. 70" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="pledge_price" label="Pledge Price">
                  <Input readOnly placeholder="eg. Price" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="required_shares"
              label="Required Shares"
              rules={[
                { required: true, message: "Please enter required shares" },
              ]}
            >
              <Input placeholder="eg. 10,000" />
            </Form.Item>
            <Form.Item
              name="start_date"
              label="Start Date"
              rules={[{ required: true, message: "Please enter start date" }]}
            >
              <DatePicker
                style={{ width: "100%" }}
                format="YYYY-MM-DD"
                onChange={handleStartDateChange}
              />
            </Form.Item>
            <Form.Item
              name="end_date"
              label="End Date"
              rules={[{ required: true, message: "Please enter end date" }]}
            >
              <DatePicker style={{ width: "100%" }} format="YYYY-MM-DD" />
            </Form.Item>
            {/* <Form.Item
              name="amount"
              label="Borrowed Amount"
              rules={[
                { required: true, message: "Please enter borrowed amount" },
              ]}
            >
              <Input placeholder="eg. 10,000" />
            </Form.Item> */}
            <Form.Item
              name="warninglineconfig"
              label="Warning Line Config"
              initialValue={1.4}
              rules={[
                { required: true, message: "Please enter warning line config" },
              ]}
            >
              <Input placeholder="eg. 1.4" />
            </Form.Item>
            <Form.Item
              name="warning_line"
              label="Warning Line"
              rules={[{ required: true, message: "Please enter warning line" }]}
            >
              <Input placeholder="eg. 1.4" />
            </Form.Item>
            <Form.Item
              name="alertlineconfig"
              label="Alert Line Config"
              initialValue={1.2}
              rules={[
                { required: true, message: "Please enter alert line config" },
              ]}
            >
              <Input placeholder="eg. 1.2" />
            </Form.Item>
            <Form.Item
              name="alert_line"
              label="Alert Line"
              rules={[{ required: true, message: "Please enter alert line" }]}
            >
              <Input placeholder="eg. 1.2" />
            </Form.Item>
            <Form.Item
              name="liquidationlineconfig"
              label="Liquidation Line Config"
              initialValue={1.1}
              rules={[
                {
                  required: true,
                  message: "Please enter liquidation line config",
                },
              ]}
            >
              <Input placeholder="eg. 1.1" />
            </Form.Item>
            <Form.Item
              name="liquidation_line"
              label="Liquidation Line"
              rules={[
                { required: true, message: "Please enter liquidation line" },
              ]}
            >
              <Input placeholder="eg. 1.1" />
            </Form.Item>
            <Form.Item className={styles.buttonItem}>
              <Button
                type="primary"
                htmlType="submit"
                className={styles.runButton}
                loading={isSimulating}
              >
                Run Simulation
              </Button>
            </Form.Item>
          </div>
          <div className={styles.requiredSharesContainer}>
            <div className={styles.requiredSharesTitle}>
              Calculation Algorithm & Results
            </div>
            <div className={styles.requiredShares}>
              <div>Required Shares</div>
              <div>Borrowed Amount / (Initial Price × Pledge Ratio)</div>
              <div>
                <Form.Item noStyle shouldUpdate>
                  {() => {
                    return form.getFieldValue("required_shares");
                  }}
                </Form.Item>
              </div>
            </div>

            <div className={styles.requiredShares}>
              <div>Warning Line</div>
              <div>Initial Price x Pledge Ratio x Warning Line Config</div>
              <div>
                <Form.Item noStyle shouldUpdate>
                  {() => {
                    return form.getFieldValue("warning_line");
                  }}
                </Form.Item>
              </div>
            </div>

            <div className={styles.requiredShares}>
              <div>Alert Line</div>
              <div>Initial Price x Pledge Ratio x Alert Line Config</div>
              <div>
                <Form.Item noStyle shouldUpdate>
                  {() => {
                    return form.getFieldValue("alert_line");
                  }}
                </Form.Item>
              </div>
            </div>

            <div className={styles.requiredShares}>
              <div>Liquidation Line</div>
              <div>Initial Price x Pledge Ratio x Liquidation Line Config</div>
              <div>
                <Form.Item noStyle shouldUpdate>
                  {() => {
                    return form.getFieldValue("liquidation_line");
                  }}
                </Form.Item>
              </div>
            </div>
          </div>
        </Form>
        {!isMobile && <RiskSimulationDetail />}
      </div>
      <div style={{ height: "219px" }} />
    </div>
  );
};

export default RiskSimulation;
