import React, { useState, useEffect } from "react";
import { Form, Input, Button, message } from "antd";
import Logo from "../../assets/Logo.svg";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { LOGIN_TOKEN } from "../../constant/login";
import styles from "./index.module.less";
import { login } from "../../services/auth";
import { useAppDispatch } from "../../hooks/useStore";
import {
  setUserInfo,
  setLoading as setStoreLoading,
} from "../../store/user/slice";

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [sliderOk, setSliderOk] = useState(false);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  useEffect(() => {
    const token = localStorage.getItem(LOGIN_TOKEN);
    if (token) {
      navigate("/", { replace: true });
    }
  }, [navigate]);

  const handleSlider = () => {
    setSliderOk(true);
    message.success(t("login.sliderVerified"));
  };

  const onFinish = (values: any) => {
    // if (!sliderOk) {
    //   message.error(t("login.sliderRequired"));
    //   return;
    // }
    setLoading(true);
    dispatch(setStoreLoading(true));

    login(values.username, values.password)
      .then((userInfo) => {
        dispatch(setUserInfo(userInfo));
        localStorage.setItem(LOGIN_TOKEN, userInfo.token);
        message.success(t("login.loginSuccess"));
        navigate("/", { replace: true });
      })
      .catch((error) => {})
      .finally(() => {
        setLoading(false);
        dispatch(setStoreLoading(false));
      });
  };

  return (
    <div className={styles.container}>
      <div className={styles.box}>
        <div className={styles.logo}>
          <img src={Logo} alt="logo" />
        </div>
        <Form
          name="login"
          onFinish={onFinish}
          layout="vertical"
          className={styles.form}
        >
          <Form.Item
            label={t("login.username")}
            name="username"
            rules={[{ required: true, message: t("login.usernameRequired") }]}
          >
            <Input placeholder="Enter username here" className={styles.input} />
          </Form.Item>
          <Form.Item
            label={t("login.password")}
            name="password"
            rules={[{ required: true, message: t("login.passwordRequired") }]}
          >
            <Input.Password
              placeholder="Enter password here"
              className={styles.input}
            />
          </Form.Item>
          <div className={styles.sliderContainer}>
            <div className={styles.sliderIcon}>&gt;&gt;</div>
            <div className={styles.sliderTrack}>{t("login.sliderText")}</div>
          </div>
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              block
              loading={loading}
              className={styles.loginBtn}
            >
              {t("login.login")}
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default Login;
