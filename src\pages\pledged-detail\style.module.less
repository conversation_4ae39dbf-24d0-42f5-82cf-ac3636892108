.pledgedDetailsContainer {
  margin: 0 auto;

  .actionSection {
    display: flex;
    gap: 12px;
    .editButton {
      padding: 8px 12px;
      gap: 4px;

      width: 76px;
      height: 36px;

      background: #161b26;
      border: 1px solid #333741;
      box-shadow: inset 0px 0px 0px 1px rgba(12, 17, 29, 0.18),
        inset 0px -2px 0px rgba(12, 17, 29, 0.05);
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      line-height: 16px;

      color: #cecfd2;
    }
  }
  .deleteButton {
    padding: 8px 12px;
    gap: 4px;

    width: 93px;
    height: 36px;

    background: #55160c;
    border: 1px solid #912018;
    box-shadow: inset 0px 0px 0px 1px rgba(12, 17, 29, 0.18),
      inset 0px -2px 0px rgba(12, 17, 29, 0.05);
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    line-height: 16px;

    color: #cecfd2;
  }
  .basicInfo {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    .label {
      font-weight: 500;
      font-size: 18px;
      line-height: 21px;

      color: #cecfd2;
      margin-bottom: 4px;
    }
    .value {
      font-weight: 600;
      font-size: 20px;
      line-height: 23px;
      display: flex;
      align-items: center;

      color: #f5f5f6;
    }

    .priceInfo {
      margin-left: 40px;
      display: flex;
      gap: 40px;

      .priceBlock {
        text-align: right;
        .priceChange {
          margin-top: 2px;
          margin-left: 8px;
          font-weight: 400;
          font-size: 16px;
          line-height: 19px;

          &.priceUp {
            color: #17b26a;
          }

          &.priceDown {
            color: #f5222d;
          }
        }
      }
    }
    .uploadContainer {
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      gap: 12px;
      width: 306px;
      :global {
        .ant-btn {
          width: 153px;
          height: 43px;
        }
      }
    }
  }

  .chartSection {
    overflow: hidden;
    display: flex;
    gap: 24px;
    min-height: 600px;
    .infoCardContainer {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .chartCard,
    .infoCard {
      height: auto;
      border: 1px solid #333741;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

      :global(.ant-card-body) {
        padding: 20px 20px 8px 20px;
        display: flex;
        flex-direction: column;
        height: 100%;
      }
    }
    .chartCard {
      flex: 1;
    }
    .infoCard {
      width: 306px;
      height: auto;
    }

    .chartHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .chartTitle {
        font-weight: 600;
        font-size: 20px;
        line-height: 23px;

        color: #f5f5f6;
      }

      .chartPeriod {
        :global {
          .ant-select-selector {
            border: none;
            background: #1f242f;
          }
        }
      }
    }

    .chart {
      flex: 1;
      flex-shrink: 0;
      height: 100%;
      width: 100%;
    }

    .infoCardTitle {
      font-weight: 600;
      font-size: 20px;
      line-height: 23px;

      color: #f5f5f6;
    }

    .infoRow {
      display: flex;
      justify-content: space-between;
      gap: 8px;
      padding: 12px 0;
      border-bottom: 1px solid #333741;

      .infoItem {
        width: 129px;
        .infoLabel {
          font-weight: 400;
          font-size: 12px;
          line-height: 14px;
          color: #94969c;
          margin-bottom: 4px;
        }

        .infoValue {
          font-weight: 600;
          font-size: 14px;
          line-height: 16px;

          color: #f5f5f6;
        }
      }
    }
    .lender {
      padding-bottom: 0;
      border-bottom: none;
    }

    .thresholdSection {
      display: flex;
      justify-content: space-between;
      padding: 12px 0;
      border-bottom: 1px solid #333741;

      .thresholdItem {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }
    }
    .thresholdLabelContainer {
      display: flex;
      align-items: center;
      gap: 2px;
      margin-bottom: 4px;

      .thresholdDot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }

      .thresholdLabel {
        font-weight: 400;
        font-size: 12px;
        line-height: 14px;
        color: #94969c;
      }

      .thresholdValue {
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
        color: #f5f5f6;
      }
    }

    .timeRemaining {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px 0;
      font-weight: 400;
      font-size: 14px;
      line-height: 16px;
      display: flex;
      align-items: center;

      color: #f5f5f6;

      .clockIcon {
        color: #f5f5f6;
        margin-right: 4px;
      }
    }
    .sendAlertButtonContainer {
      display: flex;
      gap: 12px;
    }
    .sendAlertButton {
      flex: 1;
      height: 43px;
      background: #155eef;
      box-shadow: inset 0px 0px 0px 1px rgba(12, 17, 29, 0.18),
        inset 0px -2px 0px rgba(12, 17, 29, 0.05);
      border-radius: 8px;
    }

    @media (max-width: 768px) {
      flex-direction: column;

      :global(.ant-card-body) {
        min-height: 600px;
      }
      .infoCard {
        width: 100%;
      }
    }
  }

  .bottomSection {
    margin-top: 24px;
  }
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 18px;
  color: #666;
}

.newsModal {
  :global {
    .ant-modal-content {
      padding: 40px;
    }
  }
}
