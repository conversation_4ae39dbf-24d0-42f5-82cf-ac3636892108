import type { TickerRealtimeData } from "../types/pledgedProject";
import type { SimulationResults } from "../types/RiskSimulation";
import { get, post } from "../utils/request";

export const getSimulationDetail = (id: string) => {
  return get<SimulationResults>(`/risk-simulation/${id}`);
};

export const createSimulation = (data: any) => {
  return post<{ project_id: string; data: SimulationResults[] }>(
    "/Project/createStockPledgeSimulation",
    data,
    undefined,
    undefined,
    true,
    true
  );
};
