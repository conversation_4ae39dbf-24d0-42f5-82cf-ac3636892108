import { Form, Modal, Button, Input, message } from "antd";
import React from "react";
import { CloseOutlined } from "@ant-design/icons";
import styles from "./index.module.less";
import { updateEmail } from "../../services/auth";

// 邮箱编辑弹窗组件
interface EmailModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (email: string) => void;
  initialValue: string;
}

const EmailModal: React.FC<EmailModalProps> = ({
  visible,
  onClose,
  onSave,
  initialValue,
}) => {
  const [form] = Form.useForm();

  React.useEffect(() => {
    if (visible) {
      form.setFieldsValue({ email: initialValue });
    }
  }, [visible, initialValue, form]);

  const handleSave = () => {
    form.validateFields().then((values) => {
      updateEmail(values.email).then(() => {
        onSave(values.email);
        onClose();
      });
    });
  };

  return (
    <Modal
      title="Update Email"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={480}
      closeIcon={<CloseOutlined style={{ color: "#94969C" }} />}
      className={styles.editModal}
      centered
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="email"
          label="Email"
          rules={[
            { required: true, message: "Please enter your email" },
            { type: "email", message: "Please enter a valid email" },
          ]}
        >
          <Input placeholder="Enter your email" />
        </Form.Item>

        <div className={styles.modalFooter}>
          <Button className={styles.cancelBtn} onClick={onClose}>
            Cancel
          </Button>
          <Button
            type="primary"
            className={styles.saveBtn}
            onClick={handleSave}
          >
            Save & Update
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default EmailModal;
