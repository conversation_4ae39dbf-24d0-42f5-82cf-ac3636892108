import { message, Modal, Table } from "antd";
import { useEffect, useState } from "react";
import { getSHInfo } from "../../services/pledgedProjects";
import dayjs from "dayjs";
import styles from "./style.module.less";

export default function SHInfoModal({
  visible,
  onCancel,
  projectId,
}: {
  visible: boolean;
  onCancel: () => void;
  projectId: number;
}) {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);

  useEffect(() => {
    if (visible) {
      setLoading(true);
      getSHInfo({
        project_id: projectId,
      })
        .then((res) => {
          if (!res?.length) {
            message.warning(
              "The data is currently empty. If you have uploaded it, please wait for AI analysis. If not, please upload the pdf file first for AI analysis.",
              4
            );
          }
          setData(res);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [visible]);

  return (
    <Modal
      className={styles.newsModal}
      open={visible}
      title={
        <div style={{ fontSize: 18, fontWeight: "bold", paddingBottom: 16 }}>
          <span>SH.Info</span>
        </div>
      }
      onCancel={onCancel}
      onOk={onCancel}
      footer={null}
      width="70%"
      centered
    >
      <Table
        size="middle"
        pagination={false}
        columns={[
          {
            title: "Top",
            dataIndex: "top",
            key: "top",
          },
          {
            title: "Shareholder Name",
            dataIndex: "Shareholder_name",
            key: "Shareholder_name",
          },
          {
            title: "Directly Held Warrants",
            dataIndex: "Directly_Held_Warrants",
            key: "Directly_Held_Warrants",
            // width: 400,
            // render: (text: string, record: any) => {
            //   return (
            //     <span
            //       style={{ color: record.is_red === 1 ? "#f5222d" : "inherit" }}
            //     >
            //       {text}
            //     </span>
            //   );
            // },
          },
          {
            title: "Indirectly Held Warrants",
            dataIndex: "Indirectly_Held_Warrants",
            key: "Indirectly_Held_Warrants",
            // render: (text: string) => {
            //   return (
            //     <a href={text} target="_blank" rel="noopener noreferrer">
            //       Link
            //     </a>
            //   );
            // },
          },
          {
            title: "Total Warrants Held",
            dataIndex: "Total_Warrants_Held",
            key: "Total_Warrants_Held",
          },
          {
            title: "Total Outstanding Warrants",
            dataIndex: "Total_Outstanding_Warrants",
            key: "Total_Outstanding_Warrants",
          },
        ]}
        dataSource={data}
      />
    </Modal>
  );
}
