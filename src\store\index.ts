import { configureStore } from "@reduxjs/toolkit";
import userReducer from "./user/slice";
import messageReducer from "./message/slice";
import simulationReducer from "./simulation/slice";

export const store = configureStore({
  reducer: {
    user: userReducer,
    message: messageReducer,
    simulation: simulationReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
