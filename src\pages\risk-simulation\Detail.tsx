import React, { useEffect, useState } from "react";
import styles from "./style.module.less";
import PageHeader from "../../components/PageHeader";
import { useNavigate } from "react-router-dom";
import type {
  SimulateResult,
  SimulationResults,
} from "../../types/RiskSimulation";
import CandlestickChart, {
  downBorderColor,
  downColor,
  upBorderColor,
  upColor,
} from "../pledged-detail/CandlestickChart";
import { splitData } from "../../utils/candlestickChart";
import emptyBgImage from "../../assets/risk-simulation/empty-bg.png";
import searchIcon from "../../assets/risk-simulation/search-icon.svg";
import { useSelector } from "react-redux";
import { store, type RootState } from "../../store";
import useMobile from "../../hooks/useMobile";
import dayjs from "dayjs";
import { PledgedProjectTagColors } from "../../constant/common";
import type { RiskLevel } from "../../types/pledgedProject";
import { getRiskLevelText } from "../../utils/utils";
import { setSimulationResult } from "../../store/simulation/slice";

const RiskSimulationDetail: React.FC = () => {
  const isMobileDetail = useMobile();
  const navigate = useNavigate();

  const { simulationResult, simulationData } = useSelector(
    (state: RootState) => state.simulation
  );

  const [data0, setData0] = useState<{
    categoryData: (string | number)[];
    values: (string | number)[][];
    seriesConfig?: any[];
  }>({
    categoryData: [],
    values: [],
    seriesConfig: [],
  });
  const [result, setResult] = useState<SimulateResult | null>(null);

  useEffect(() => {
    if (simulationResult) {
      const sortedSimulationResult = [...simulationResult].sort((a, b) =>
        dayjs(a.date).diff(dayjs(b.date))
      );
      const categoryData = sortedSimulationResult.map((item) =>
        dayjs(item.date).format("MM-DD-YYYY")
      );
      const values = [
        sortedSimulationResult.map((item) => item.close),
        sortedSimulationResult.map((item) => item.low),
        [],
      ];
      const markPointData = {
        symbolSize: 68,
        symbol: "pin",
        label: {
          fontSize: 10,
          fontWeight: "bold",
          formatter: function (param: any) {
            return getRiskLevelText(param.data.customData.line as RiskLevel);
            // return (
            //   (param?.data?.coord != null || param?.data?.coord != undefined) &&
            //   param.data.coord[1]
            // );
          },
        },
      };
      const filterData = simulationResult.filter(
        (item) =>
          item.simulateResult &&
          item.simulateResult.line?.toLowerCase() !== "low"
      );
      setData0({
        categoryData,
        values,
        seriesConfig: [
          {
            symbol: "none",
            name: "close",
            markLine: {
              symbol: "none",
              data: [
                {
                  yAxis: simulationData?.warning_line,
                  name: "Warning Line",
                  lineStyle: {
                    color: PledgedProjectTagColors.critical.backgroundColor,
                  },
                },
              ],
              label: {
                formatter: "{b}: {c}",
                position: "insideEndTop",
              },
            },
            markPoint: {
              ...markPointData,
              data: filterData.map((item) => ({
                name: item.simulateResult.status,
                coord: [
                  dayjs(item.simulateResult.time).format("MM-DD-YYYY"),
                  item.close,
                ],
                itemStyle: {
                  color:
                    PledgedProjectTagColors[
                      item.simulateResult.line?.toLowerCase() as RiskLevel
                    ]?.backgroundColor,
                },
                customData: {
                  ...item.simulateResult,
                },
              })),
            },
          },
          {
            symbol: "none",
            name: "low",
            markLine: {
              symbol: "none",
              data: [
                {
                  yAxis: simulationData?.alert_line,
                  name: "Alert Line",
                  lineStyle: {
                    color: PledgedProjectTagColors.high.backgroundColor,
                  },
                },
              ],
              label: {
                formatter: "{b}: {c}",
                position: "insideEndTop",
              },
            },
            markPoint: {
              ...markPointData,
              data: filterData.map((item) => ({
                name: item.simulateResult.status,
                coord: [
                  dayjs(item.simulateResult.time).format("MM-DD-YYYY"),
                  item.low,
                ],
                itemStyle: {
                  color:
                    PledgedProjectTagColors[
                      item.simulateResult.line?.toLowerCase() as RiskLevel
                    ]?.backgroundColor,
                },
                customData: {
                  ...item.simulateResult,
                },
              })),
            },
          },
          {
            symbol: "none",
            name: "line",
            markLine: {
              symbol: "none",
              data: [
                {
                  yAxis: simulationData?.liquidation_line,
                  name: "Liquidation Line",
                  lineStyle: {
                    color: PledgedProjectTagColors.medium.backgroundColor,
                  },
                },
              ],
              label: {
                formatter: "{b}: {c}",
                position: "insideEndTop",
              },
            },
          },
        ],
      });
    }
  }, [simulationResult, simulationData]);

  useEffect(() => {
    return () => {
      if (isMobileDetail) {
        store.dispatch(setSimulationResult([]));
      }
    };
  }, [isMobileDetail]);

  const handleChartPointChange = (params: any) => {
    console.log(params);
    if (params.componentType === "markPoint") {
      setResult(params.data.customData);
    }
  };

  return (
    <div className={styles.riskSimulationDetail}>
      {isMobileDetail && <PageHeader onBack={() => navigate(-1)} />}

      <div className={styles.resultsCard}>
        <div className={styles.cardTitle}>Simulation Results</div>
        {data0.categoryData.length > 0 ? (
          <>
            <CandlestickChart
              name={"Simulation Results"}
              chartType="line"
              data={data0}
              className={styles.chartContainer}
              onClick={handleChartPointChange}
            />

            <div
              className={styles.metricsContainer}
              style={{ display: isMobileDetail && !result ? "none" : "block" }}
            >
              {result && (
                <div
                  className={styles.metricCard}
                  style={{
                    borderColor:
                      PledgedProjectTagColors[
                        result?.line?.toLowerCase() as RiskLevel
                      ]?.backgroundColor,
                  }}
                >
                  <div className={styles.metricTop}>
                    <div className={styles.metricTime}>
                      <div className={styles.metricTitle}>Time</div>
                      <div className={styles.metricValue}>
                        {dayjs(result?.time).format("MM-DD-YYYY")}
                      </div>
                    </div>
                    <div className={styles.metricStatus}>
                      <div className={styles.metricTitle}>Status</div>
                      <div className={styles.metricValue}>
                        <span
                          className={`${styles.riskTag}`}
                          style={{
                            background:
                              PledgedProjectTagColors[
                                result?.line?.toLowerCase() as RiskLevel
                              ]?.backgroundColor,
                            color:
                              PledgedProjectTagColors[
                                result?.line?.toLowerCase() as RiskLevel
                              ]?.color,
                          }}
                        >
                          {result?.line &&
                            getRiskLevelText(result?.line as RiskLevel)}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className={styles.metricContent}>
                    <div className={styles.metricTitle}>Stock Price</div>
                    <div className={styles.metricValue} title={result?.message}>
                      {result?.message}
                    </div>
                  </div>
                  <div className={styles.metricBottom}>
                    <div className={styles.metricTitle}>LTV</div>
                    <div className={styles.metricValue}>{result?.LTV}</div>
                  </div>
                </div>
              )}
            </div>
          </>
        ) : (
          <div className={styles.resultsCardEmpty}>
            <div className={styles.emptyImageContainer}>
              <img src={emptyBgImage} alt="" />
              <div className={styles.emptyContainerText}>
                <img src={searchIcon} alt="" />
                <div className={styles.emptyContainerTextTitle}>
                  You have not set any simulation parameters yet.
                </div>
                <div className={styles.emptyContainerTextDescription}>
                  Refine the details to generate the risk analysis report
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RiskSimulationDetail;
