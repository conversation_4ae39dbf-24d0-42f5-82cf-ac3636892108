.riskSimulationPage {
  .title {
    font-weight: 700;
    font-size: 28px;
    line-height: 33px;
    color: #ececed;
    margin-bottom: 24px;
  }

  .contentContainer {
    display: grid;
    grid-template-columns: 306px 1fr;
    gap: 24px;

    @media (max-width: 992px) {
      grid-template-columns: 1fr;
    }
  }

  .form {
    width: 100%;
    :global {
      .ant-form-item {
        margin-bottom: 16px;
      }
    }
  }
  .parametersCard {
    border-radius: 6px;
    padding: 20px;
    height: fit-content;
    border: 1px solid #333741;

    .cardTitle {
      font-weight: 600;
      font-size: 18px;
      line-height: 22px;
      color: #f5f5f6;
      margin-bottom: 24px;
    }
    .buttonItem {
      margin-bottom: 0 !important;
    }

    .runButton {
      width: 100%;
      height: 43px;
      font-size: 16px;
      font-weight: 500;
    }
  }
  .requiredSharesContainer {
    width: 100%;
    max-width: 640px;
    position: absolute;
    padding-bottom: 40px;
    .requiredSharesTitle {
      padding: 24px 0 12px 0;
      font-weight: 600;
      font-size: 20px;
      line-height: 23px;
      display: flex;
      align-items: center;
      color: #f5f5f6;
    }
    .requiredShares {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
      padding: 0 24px;
      border-bottom: 1px solid #333741;
      @media (max-width: 992px) {
        margin-top: 8px;
        padding: 0 0 8px;
      }
    }
  }
}

.resultsCard {
  height: 100%;
  border: 1px solid #333741;
  border-radius: 6px;
  padding: 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .resultsCardEmpty {
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 100%;
    .emptyImageContainer {
      position: relative;
      width: 480px;
      height: 574px;
      object-fit: contain;
      > img {
        width: auto;
        height: 100%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
    .emptyContainerText {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      @media (max-width: 420px) {
        padding-top: 24px;
      }
      img {
        width: 48px;
        height: 48px;
      }
      &Title {
        margin-top: 16px;
        font-weight: 600;
        font-size: 16px;
        line-height: 19px;
        text-align: center;
        color: #f5f5f6;
      }
      &Description {
        margin-top: 4px;
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
        text-align: center;
        color: #94969c;
      }
    }
  }
  .cardTitle {
    font-weight: 600;
    font-size: 18px;
    line-height: 22px;
    color: #f5f5f6;
    margin-bottom: 30px;
  }

  .chartContainer {
    flex: 1;
    margin-bottom: 12px;
  }

  .metricsContainer {
    display: grid;
    gap: 16px;
    height: 204px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .metricCard {
    height: 204px;
    padding: 20px;
    border: 1px solid #333741;
    border-radius: 6px;
    .metricTitle {
      font-style: normal;
      font-weight: 400;
      font-size: 12px;
      line-height: 14px;
      color: #94969c;
    }

    .metricValue {
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 16px;
      color: #f5f5f6;
      // 超出2行省略
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .riskTag {
      border-radius: 6px;
      font-weight: 600;
      font-size: 12px;
      color: #ffffff;
      text-align: center;
      line-height: 14px;
      display: inline-block;
      padding: 4px 8px;
    }

    .metricTop {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      align-items: center;
      margin-bottom: 16px;
      margin-top: 12px;
      gap: 8px;

      .metricTime {
        width: 89px;
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      .metricStatus {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }
    }

    .metricContent {
      display: flex;
      flex-direction: column;
      gap: 4px;
      margin: 8px 0;
    }

    .metricBottom {
      display: flex;
      flex-direction: column;
      gap: 4px;
      margin-bottom: 12px;
    }

    &.marginCall {
      box-shadow: inset 0px 4px 4px 100px rgba(23, 178, 106, 0.1);
    }

    &.liquidation {
      box-shadow: inset 0px 4px 4px 100px rgba(240, 68, 56, 0.1);
    }

    &.recovery {
      box-shadow: inset 0px 4px 4px 100px rgba(48, 107, 255, 0.1);
    }
  }
}

.riskSimulationDetail {
  display: flex;
  flex-direction: column;
  height: 100%;
}
