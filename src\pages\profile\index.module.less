.profileContainer {
  width: 100%;
  margin: 0 auto;
}

.pageTitle {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: rgba(255, 255, 255, 0.9);
}

.profileCard {
  border-radius: 6px;
  padding: 40px;
  border: 1px solid #333741;
}

.profileHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}

.avatarLarge {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: #1d2536;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
  margin-top: 64px;
}

.userDetails {
  margin-top: 24px;
  flex: 1;
}

.userName {
  height: 23px;
  line-height: 23px;
  font-size: 22px;
  font-weight: 600;
  margin: 0;
  color: #ffffff;
}

.userRole {
  font-weight: 400;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.65);
  text-align: center;
  margin-top: 4px;
  height: 16px;
  line-height: 16px;
}

.profileInfo {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.infoLabel {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
}

.infoValue {
  align-items: center;
  font-size: 16px;
  color: white;
  padding-bottom: 8px;

  .editIcon {
    margin-left: 6px;
    font-size: 16px;
    color: #1677ff;
    cursor: pointer;

    &:hover {
      color: #4096ff;
    }
  }
}

.changePasswordBtn {
  padding: 0;
  color: #1677ff;
  font-weight: 500;

  &:hover {
    color: #4096ff;
  }
}

/* 编辑弹窗样式 */
.editModal {
  :global {
    .ant-modal-content {
      background-color: #111827;
      border: 1px solid #1f2937;
      border-radius: 8px;
    }

    .ant-modal-header {
      background-color: #111827;
      border-bottom: none;
      padding: 16px 24px;

      .ant-modal-title {
        color: white;
        font-size: 18px;
        font-weight: 600;
      }
    }

    .ant-modal-body {
      padding: 16px 24px 24px;
    }

    .ant-form-item-label > label {
      color: white;
    }

    .ant-input {
      background-color: #1d2536;
      border: 1px solid #333741;
      color: white;
      border-radius: 4px;
      height: 40px;

      &::placeholder {
        color: rgba(255, 255, 255, 0.4);
      }

      &:focus,
      &:hover {
        border-color: #1677ff;
      }
    }
  }
}

.modalFooter {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
}

.cancelBtn {
  background-color: #1d2536;
  border: 1px solid #333741;
  color: white;
  height: 40px;
  width: 48%;
  border-radius: 4px;

  &:hover {
    background-color: #252e3f;
    border-color: #333741;
    color: white;
  }
}

.saveBtn {
  background-color: #1677ff;
  border: none;
  height: 40px;
  width: 48%;
  border-radius: 4px;

  &:hover {
    background-color: #4096ff;
  }
}
