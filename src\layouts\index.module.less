.layout {
  min-height: 100vh;
  background: #0c111d;
  :global {
    .ant-select-prefix {
      display: flex;
      align-items: center;
    }
  }
  .content {
    padding: 20px 40px;
    min-height: 280px;
    overflow: auto;
    @media (max-width: 980px) {
      padding: 20px 32px;
    }
    @media (max-width: 681px) {
      padding: 20px 16px;
    }
  }
}

.sider {
  overflow: hidden;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  background: #0c111d;
  border-right: 1px solid #1f242f;
  z-index: 11;
  :global {
    .ant-menu-dark {
      background: #0c111d;
    }
  }
  .menu {
    padding: 0 15px;
  }
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;

  img {
    transition: all 0.2s;
  }
}

.header {
  width: auto;
  margin: 0 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  position: sticky;
  top: 0;
  z-index: 1;
  border-bottom: 1px solid #333741;
  height: 56px;
  @media (max-width: 980px) {
    margin: 0 32px;
  }
  @media (max-width: 681px) {
    margin: 0 16px;
  }
}

.userProfile {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 16px;
}

.avatarContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
  border-radius: 4px;
  width: 100%;
  margin-bottom: 10px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #1d2536;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: bold;
  margin-right: 12px;
}

.userInfo {
  flex: 1;
  overflow: hidden;
  margin-left: 12px;
}

.userName {
  font-size: 14px;
  font-weight: 500;
  color: white;
  margin: 0;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.userRole {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.65);
  margin-top: 4px;
  line-height: 1.2;
}

.logoutButton {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  height: 36px;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
}
