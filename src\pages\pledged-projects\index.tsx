import React, { useEffect, useRef, useState } from "react";
import { Typography, Input, Button, Table, message, Modal } from "antd";
import {
  SearchOutlined,
  PlusOutlined,
  ExportOutlined,
  ClockCircleOutlined,
  EditOutlined,
  DeleteOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import AddOrEditProjectModal from "./AddOrEditProjectModal";
import styles from "./style.module.less";
import {
  createPledgeProject,
  deletePledgeProject,
  getPledgeProjectList,
  updatePledgeProject,
} from "../../services/pledgedProjects";
import type { PledgedProject, RiskLevel } from "../../types/pledgedProject";
import { PledgedProjectTagColors } from "../../constant/common";
import { useLocation, useNavigate } from "react-router-dom";
import dayjs from "dayjs";

import { calculateTimeLeft, getRiskLevelText } from "../../utils/utils";
import Icon from "../../components/Icon";

const { Title } = Typography;

const PledgedProjects: React.FC = () => {
  const { search } = useLocation();
  const uid = new URLSearchParams(search).get("uid");
  const navigate = useNavigate();
  const [searchText, setSearchText] = useState<string>("");
  const originalData = useRef<PledgedProject[]>([]);
  const [data, setData] = useState<PledgedProject[]>([]);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [currentProject, setCurrentProject] = useState<PledgedProject | null>(
    null
  );

  const getList = () => {
    getPledgeProjectList({ user_id: uid }).then(
      (data: { project_list: PledgedProject[] }) => {
        setData(data.project_list);
        originalData.current = data.project_list;
      }
    );
  };

  useEffect(() => {
    getList();
  }, []);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    const filteredData = originalData.current.filter((item) =>
      item.stock_name.toLowerCase().includes(value.toLowerCase())
    );
    setData(filteredData);
  };

  const handleAddProject = () => {
    setIsModalVisible(true);
  };

  const handleAddOrEditCancel = () => {
    setIsModalVisible(false);
    setCurrentProject(null);
  };

  const handleAddOrEditProject = (values: any) => {
    getList();
    setIsModalVisible(false);
    setCurrentProject(null);
  };

  const handleExport = () => {
    console.log("Export data");
  };

  const handleEdit = (id: number) => {
    const project = data.find((item) => item.project_id === id);
    if (project) {
      // 为编辑项目添加默认值
      const projectWithDefaults = {
        ...project,
        sharedPledged: project.stock_price,
        warningLine: project.warning_line,
        alertLine: project.alert_line,
        liquidationLine: project.liquidation_line,
      };
      setCurrentProject(projectWithDefaults);
      setIsModalVisible(true);
    }
  };

  const handleDelete = (id: number) => {
    const project = data.find((item) => item.project_id === id);
    if (!project) return;

    Modal.confirm({
      title: "Delete Pledged Asset",
      content:
        "Are you sure you want to delete this pledged asset? This action cannot be undone.",
      okText: "Delete Asset",
      icon: null, // 移除默认图标
      closable: true,
      closeIcon: <CloseOutlined style={{ color: "#94969C" }} />,
      centered: true,
      cancelText: "Cancel",
      onOk: () => {
        deletePledgeProject({ project_id: id }).then(() => {
          getList();
          message.success("项目已删除");
        });
      },
    });
  };

  const columns = [
    {
      title: "Stock",
      dataIndex: "stock",
      key: "stock",
      render: (_: any, record: PledgedProject) => (
        <div className={styles.stockCell}>
          <div className={styles.stockName}>{record.stock_name}</div>
          <div className={styles.stockId}>{record.stock_code}</div>
        </div>
      ),
    },
    {
      title: "Current Price",
      dataIndex: "current_stock_price",
      key: "current_stock_price",
      render: (price: number) => price,
    },
    {
      title: "Risk Level",
      dataIndex: "safety_status",
      key: "safety_status",
      render: (level: RiskLevel) => (
        <span
          className={`${styles.riskTag}`}
          style={{
            background:
              PledgedProjectTagColors[level?.toLowerCase() as RiskLevel]
                ?.backgroundColor,
            color:
              PledgedProjectTagColors[level?.toLowerCase() as RiskLevel]?.color,
          }}
        >
          {getRiskLevelText(level)}
        </span>
      ),
    },
    {
      title: "Borrower",
      dataIndex: "borrower_id",
      key: "borrower_id",
      render: (id: string, record: PledgedProject) =>
        record.relations?.[0]?.borrower_name,
    },
    {
      title: "Lender",
      dataIndex: "lender_id",
      key: "lender_id",
      render: (id: string, record: PledgedProject) =>
        record.relations?.map((item) => item.lender_name).join(", "),
    },
    {
      title: "Time Left",
      dataIndex: "starttime",
      key: "starttime",
      render: (time: string, record: PledgedProject) => {
        return (
          <div className={styles.timeCell}>
            <Icon
              name="clock"
              size={16}
              color="#fff"
              className={styles.clockIcon}
            />
            <span>{calculateTimeLeft(record.start_date, record.end_date)}</span>
          </div>
        );
      },
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: any, record: PledgedProject) => (
        <div className={styles.actionCell}>
          <EditOutlined
            className={styles.actionButton}
            onClick={(e) => {
              e.stopPropagation();
              handleEdit(record.project_id);
            }}
          />
          <DeleteOutlined
            className={`${styles.actionButton} ${styles.deleteButton}`}
            onClick={(e) => {
              e.stopPropagation();
              handleDelete(record.project_id);
            }}
          />
        </div>
      ),
    },
  ];

  return (
    <div className={styles.pledgedProjectsPage}>
      <div className={`${styles.header} common-page-header-flex`}>
        <Title level={1} className={styles.title}>
          Pledged Project
        </Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          className={styles.addButton}
          onClick={handleAddProject}
        >
          Add New Pledged Project
        </Button>
      </div>

      <div className={styles.searchExportRow}>
        <Input
          placeholder="Search"
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={handleSearch}
          className={styles.searchInput}
        />
        {/* <Button
          type="default"
          icon={<Icon name="export" size={16} color="#CECFD2" />}
          onClick={handleExport}
          className={"export-button"}
        >
          Export
        </Button> */}
      </div>

      <Table
        className={styles.table}
        columns={columns}
        dataSource={data}
        rowKey="id"
        pagination={false}
        size="middle"
        onRow={(record) => ({
          onClick: () => {
            navigate(`/pledged-detail/${record.project_id}`);
          },
        })}
      />

      <AddOrEditProjectModal
        visible={isModalVisible}
        projectData={currentProject}
        onCancel={handleAddOrEditCancel}
        onOk={handleAddOrEditProject}
      />
    </div>
  );
};

export default PledgedProjects;
