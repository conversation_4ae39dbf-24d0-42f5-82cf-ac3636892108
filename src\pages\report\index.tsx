import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Button,
  Select,
  Table,
  Tag,
  type TablePaginationConfig,
} from "antd";
import styles from "./style.module.less";
import Icon from "../../components/Icon";
import type { StockOption, StockReport } from "../../types/Report";
import { exportReport, getReports, getStocks } from "../../services/report";
import dayjs from "dayjs";
import { PledgedProjectTagColors } from "../../constant/common";
import type { RiskLevel } from "../../types/pledgedProject";
import { getRiskLevelText } from "../../utils/utils";

const { Title } = Typography;

const ReportPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>("risk-control");
  const [reports, setReports] = useState<StockReport[]>([]);
  const [stocks, setStocks] = useState<StockOption[]>([]);
  const [currentStock, setCurrentStock] = useState<string | undefined>(
    undefined
  );
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const fetchReports = (query: any) => {
    getReports({
      page: pagination.current,
      size: pagination.pageSize,
      project_id: currentStock,
      ...query,
    }).then((res) => {
      setReports(res.data);
      setPagination({
        ...pagination,
        total: res.total,
      });
    });
  };

  const fetchStocks = () => {
    getStocks().then((res) => {
      setStocks(res);
    });
  };

  const handleStockChange = (value: string) => {
    console.log("Selected stock:", value);
    setCurrentStock(value);
    fetchReports({ project_id: value });
  };

  useEffect(() => {
    fetchReports({});
    fetchStocks();
  }, []);

  useEffect(() => {
    fetchReports({});
  }, [pagination.current, pagination.pageSize]);

  // 表格列定义
  const columns = [
    {
      title: "Date/Time",
      dataIndex: "date",
      key: "date",
      render: (_: any, record: StockReport) => (
        <>
          <div className={styles.columnTitle}>
            {dayjs(record.create_time).format("DD/MM/YYYY")}
          </div>
          <div className={styles.columnValue}>
            {dayjs(record.create_time).format("HH:mm:ss")}
          </div>
        </>
      ),
      // sorter: (a: StockReport, b: StockReport) =>
      //   dayjs(a.create_time).valueOf() - dayjs(b.create_time).valueOf(),
    },
    {
      title: "Stock",
      dataIndex: "stock",
      key: "stock",
      render: (_: any, record: StockReport) => (
        <>
          <div className={styles.columnTitle}>{record.stock_name}</div>
          <div className={styles.columnValue}>{record.stock_code}</div>
        </>
      ),
      // sorter: (a: StockReport, b: StockReport) =>
      //   a.stock_code.localeCompare(b.stock_code),
    },
    {
      title: "Stock Price",
      dataIndex: "stock_price",
      key: "stock_price",
      // sorter: (a: StockReport, b: StockReport) =>
      //   Number(a.stock_price) - Number(b.stock_price),
    },
    {
      title: "Current Price",
      dataIndex: "current_stock_price",
      key: "current_stock_price",
      // sorter: (a: StockReport, b: StockReport) =>
      //   Number(a.current_stock_price) - Number(b.current_stock_price),
    },
    {
      title: "Risk Level",
      dataIndex: "status",
      key: "status",
      render: (status: string) => {
        return (
          <span
            className={`${styles.riskTag}`}
            style={{
              background:
                PledgedProjectTagColors[status?.toLowerCase() as RiskLevel]
                  ?.backgroundColor,
              color:
                PledgedProjectTagColors[status?.toLowerCase() as RiskLevel]
                  ?.color,
            }}
          >
            {getRiskLevelText(status as RiskLevel)}
          </span>
        );
      },
      // sorter: (a: StockReport, b: StockReport) =>
      //   a.status.localeCompare(b.status),
    },
    {
      title: "Old Pledge Rate",
      dataIndex: "old_pledge_rate",
      key: "old_pledge_rate",
      // sorter: (a: StockReport, b: StockReport) =>
      //   Number(a.old_pledge_rate) - Number(b.old_pledge_rate),
    },
    {
      title: "New Pledge Rate",
      dataIndex: "pledge_rate",
      key: "pledge_rate",
      // sorter: (a: StockReport, b: StockReport) =>
      //   Number(a.pledge_price) - Number(b.pledge_price),
    },
    {
      title: "Project Message",
      dataIndex: "project_msg",
      key: "project_msg",
      width: 300,
      render: (project_msg: string) => {
        return (
          <div className={styles.projectMsg} title={project_msg}>
            {project_msg}
          </div>
        );
      },
    },
  ];

  // 处理导出功能
  const handleExport = () => {
    console.log("导出报告");
    exportReport({ project_id: currentStock }).then((res) => {
      const a = document.createElement("a");
      a.href = res.download_url;
      a.click();
    });
  };

  // 处理选项卡切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  const handleTableChange = (pagination: TablePaginationConfig) => {
    console.log("Table pagination changed:", pagination);
    setPagination(pagination);
  };

  return (
    <div className={styles.reportPage}>
      <Title level={1} className={styles.pageTitle}>
        Report
      </Title>

      {/* 选项卡 */}
      <div className={styles.tabsContainer}>
        <Button
          type={activeTab === "risk-control" ? "primary" : "text"}
          className={styles.tabButton}
          onClick={() => handleTabChange("risk-control")}
        >
          Risk Control Report
        </Button>
        {/* <Button
          type={activeTab === "monthly-reconciliation" ? "primary" : "text"}
          className={styles.tabButton}
          onClick={() => handleTabChange("monthly-reconciliation")}
        >
          Monthly Reconciliation Report
        </Button> */}
      </div>

      {/* 筛选和导出 */}
      <div className={styles.filterContainer}>
        <div className={styles.filterItem}>
          <Select
            placeholder="Select stock"
            className={styles.stockSelect}
            allowClear
            prefix={
              <span className={styles.filterLabel}>Filter by stock:</span>
            }
            value={currentStock}
            onChange={handleStockChange}
          >
            {stocks.map((item) => (
              <Select.Option key={item.project_id} value={item.project_id}>
                <span className={styles.stockName}>{item.stock_name}</span>
                <span className={styles.stockCode}>{item.project_id}</span>
              </Select.Option>
            ))}
          </Select>
        </div>
        <Button
          type="default"
          icon={<Icon name="export" size={16} color="#CECFD2" />}
          onClick={handleExport}
          className={"export-button"}
        >
          Export
        </Button>
      </div>

      {/* 报告表格 */}
      <div className={styles.tableContainer}>
        <Table
          columns={columns}
          dataSource={reports}
          className={styles.reportTable}
          rowClassName={styles.tableRow}
          size="middle"
          onChange={handleTableChange}
          pagination={pagination}
        />
      </div>
    </div>
  );
};

export default ReportPage;
