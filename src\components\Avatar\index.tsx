import React from "react";
import { Avatar as AntdAvatar } from "antd";
import styles from "./style.module.less";

interface AvatarProps {
  src?: string; // 图片地址
  name?: string; // 用户名
  size?: number | "large" | "small" | "default"; // 头像大小
  className?: string; // 自定义类名
  style?: React.CSSProperties; // 自定义样式
}

/**
 * 头像组件
 * 支持传递用户图片地址或者用户名
 * 如果是用户名头像截取名字首字母，如：system admin 显示 SA，systemadmin 显示 SY，中文显示第一个文字
 */
const Avatar: React.FC<AvatarProps> = ({
  src,
  name = "",
  size = "default",
  className,
  style,
}) => {
  // 生成头像显示文本
  const getAvatarText = (name: string): string => {
    if (!name) return "";

    // 检查是否包含中文字符
    const hasChinese = /[\u4e00-\u9fa5]/.test(name);

    if (hasChinese) {
      // 中文显示第一个字
      return name.charAt(0);
    } else if (name.includes(" ")) {
      // 有空格的英文名，取每个单词的首字母
      return name
        .split(" ")
        .map((part) => part.charAt(0))
        .join("")
        .toUpperCase();
    } else {
      // 无空格的英文名，取前两个字母
      return name.substring(0, 2).toUpperCase();
    }
  };

  // 根据名字生成固定的颜色
  const getColorFromName = (name: string): string => {
    if (!name) return "#1677ff"; // 默认蓝色

    // 颜色列表
    const colors = [
      "#f56a00", // 橙色
      "#7265e6", // 紫色
      "#ffbf00", // 黄色
      "#00a2ae", // 青色
      "#1677ff", // 蓝色
      "#52c41a", // 绿色
      "#eb2f96", // 粉色
      "#fa541c", // 红色
    ];

    // 使用名字的字符编码总和来选择颜色
    let sum = 0;
    for (let i = 0; i < name.length; i++) {
      sum += name.charCodeAt(i);
    }

    return colors[sum % colors.length];
  };

  return (
    <AntdAvatar
      src={src}
      size={size}
      className={`${styles.avatar} ${className}`}
      style={{
        backgroundColor: !src ? "#1F242F" : undefined,
        ...style,
      }}
    >
      {!src && getAvatarText(name)}
    </AntdAvatar>
  );
};

export default Avatar;
