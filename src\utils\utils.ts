import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import { PledgedProjectColors } from "../constant/common";
import type { RiskLevel } from "../types/pledgedProject";

dayjs.extend(duration);

// 获取相对日期（今天、昨天或具体日期）
export const getRelativeDate = (dateString: string) => {
  const today = dayjs();
  const date = dayjs(dateString);

  if (date.format("YYYY-MM-DD") === today.format("YYYY-MM-DD")) {
    return "Today";
  } else if (
    date.format("YYYY-MM-DD") === today.subtract(1, "day").format("YYYY-MM-DD")
  ) {
    return "Yesterday";
  } else {
    // 返回格式如 "18 April" 的日期
    return date.format("D MMMM");
  }
};

export const getRiskLevelColor = (level: RiskLevel): string => {
  const _level = level?.toLowerCase?.()?.split("(")[0];
  switch (_level) {
    case "critical":
      return PledgedProjectColors.Critical;
    case "high":
      return PledgedProjectColors.High;
    case "medium":
      return PledgedProjectColors.Medium;
    case "low":
      return PledgedProjectColors.Low;
    default:
      return PledgedProjectColors.Default;
  }
};

export const getRiskLevelText = (level: RiskLevel): string => {
  const _level = level?.toLowerCase?.()?.split("(")[0];
  switch (_level) {
    case "critical":
      return "Critical";
    case "high":
      return "High";
    case "medium":
      return "Medium";
    case "low":
      return "Low";
    default:
      return level;
  }
};

export const calculateTimeLeft = (startDate: string, endDate: string) => {
  const diff = dayjs.duration(dayjs(endDate).diff(dayjs(startDate)));

  const days = Math.floor(diff.asDays());
  const hours = diff.hours();
  const minutes = diff.minutes();
  return `${days.toString().padStart(2, "0")}d ${hours
    .toString()
    .padStart(2, "0")}h ${minutes.toString().padStart(2, "0")}m`;
};
