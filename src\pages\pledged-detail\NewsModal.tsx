import { Modal, Table } from "antd";
import { useEffect, useState } from "react";
import { getProjectNews } from "../../services/pledgedProjects";
import dayjs from "dayjs";
import styles from "./style.module.less";

export default function NewsModal({
  visible,
  onCancel,
  stock_code,
}: {
  visible: boolean;
  onCancel: () => void;
  stock_code: string;
}) {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);

  useEffect(() => {
    if (visible) {
      setLoading(true);
      getProjectNews({
        stock_code,
      })
        .then((res) => {
          setData(res);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [visible]);

  return (
    <Modal
      className={styles.newsModal}
      open={visible}
      title={
        <div style={{ fontSize: 18, fontWeight: "bold", paddingBottom: 16 }}>
          <span>Project News</span>
        </div>
      }
      onCancel={onCancel}
      onOk={onCancel}
      footer={null}
      width={940}
      centered
    >
      <Table
        size="middle"
        loading={loading}
        pagination={false}
        columns={[
          {
            title: "Stock Name",
            dataIndex: "stock_name",
            key: "stock_name",
          },
          {
            title: "Stock Code",
            dataIndex: "stock_code",
            key: "stock_code",
          },
          {
            title: "News Title",
            dataIndex: "news_title",
            key: "news_title",
            width: 400,
            render: (text: string, record: any) => {
              return (
                <span
                  style={{ color: record.is_red === 1 ? "#f5222d" : "inherit" }}
                >
                  {text}
                </span>
              );
            },
          },
          {
            title: "Link",
            dataIndex: "new_url",
            key: "new_url",
            render: (text: string) => {
              return (
                <a href={text} target="_blank" rel="noopener noreferrer">
                  Link
                </a>
              );
            },
          },
          {
            title: "Date",
            dataIndex: "create_time",
            key: "create_time",
            render: (text: string) => {
              return dayjs(text).format("DD/MM/YYYY");
            },
          },
        ]}
        dataSource={data}
      />
    </Modal>
  );
}
