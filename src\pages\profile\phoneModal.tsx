import { Form, Modal, Button, Input } from "antd";
import React from "react";
import { CloseOutlined } from "@ant-design/icons";
import styles from "./index.module.less";
import { updatePhone } from "../../services/auth";

// 手机号编辑弹窗组件
export interface PhoneModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (phone: string) => void;
  initialValue: string;
}

const PhoneModal: React.FC<PhoneModalProps> = ({
  visible,
  onClose,
  onSave,
  initialValue,
}) => {
  const [form] = Form.useForm();

  React.useEffect(() => {
    if (visible) {
      const parts = initialValue.split(" ");
      form.setFieldsValue({
        phonePrefix: parts[0],
        phoneNumber: parts[1] || "",
      });
    }
  }, [visible, initialValue, form]);

  const handleSave = () => {
    form.validateFields().then((values) => {
      updatePhone(`${values.phonePrefix} ${values.phoneNumber}`).then(() => {
        onSave(`${values.phonePrefix} ${values.phoneNumber}`);
        onClose();
      });
    });
  };

  return (
    <Modal
      title="Update Mobile Number"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={480}
      closeIcon={<CloseOutlined style={{ color: "#94969C" }} />}
      className={styles.editModal}
      centered
    >
      <Form form={form} layout="vertical">
        <Form.Item label="Mobile Number">
          <Input.Group compact>
            <Form.Item
              name="phonePrefix"
              noStyle
              rules={[{ required: true, message: "Please enter country code" }]}
            >
              <Input style={{ width: "20%" }} placeholder="+60" />
            </Form.Item>
            <Form.Item
              name="phoneNumber"
              noStyle
              rules={[{ required: true, message: "Please enter phone number" }]}
            >
              <Input style={{ width: "80%" }} placeholder="123456789" />
            </Form.Item>
          </Input.Group>
        </Form.Item>

        <div className={styles.modalFooter}>
          <Button className={styles.cancelBtn} onClick={onClose}>
            Cancel
          </Button>
          <Button
            type="primary"
            className={styles.saveBtn}
            onClick={handleSave}
          >
            Save & Update
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default PhoneModal;
