:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #0c111d;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
#root {
  width: 100%;
}
a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}
p {
  margin: 0;
  padding: 0;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
.ant-typography {
  font-weight: 700;
  font-size: 28px;
  line-height: 33px;
  color: #ececed;
  margin: 0;
  padding: 0;
}

/* 添加对Modal的全局样式重写 */
html[data-theme="dark"] {
  --antd-modal-bg: #0c111d;
  --antd-modal-title: #f5f5f6;
  --antd-modal-text: #cecfd2;
  --antd-modal-close: #94969c;
  --antd-btn-default-bg: #0c111d;
  --antd-btn-default-color: rgba(255, 255, 255, 0.85);
  --antd-btn-default-border: #2e3242;
  --antd-modal-border: #1f2937;
  --antd-logout-btn-bg: #e63946;
  --antd-logout-btn-border: #e63946;
}
.ant-btn {
  border-radius: 8px;
}
.ant-modal-confirm {
  .ant-modal-content {
    padding: 40px;
    .ant-modal-confirm-paragraph {
      display: block;
      .ant-modal-confirm-title {
        font-weight: 600;
        font-size: 20px;
      }
      .ant-modal-confirm-content {
        font-weight: 400;
        font-size: 18px;
        margin-top: 40px;
      }
    }
    .ant-modal-confirm-btns {
      margin-top: 64px;
      display: flex;
      justify-content: space-between;
      .ant-btn {
        width: 190px;
        background-color: var(--antd-modal-bg);
        border: 1px solid var(--antd-modal-border);
        color: var(--antd-modal-text);
        width: 190px;
        height: 44px;
        background: #161b26;
        border: 1px solid #333741;
        box-shadow: inset 0px 0px 0px 1px rgba(12, 17, 29, 0.18),
          inset 0px -2px 0px rgba(12, 17, 29, 0.05);
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;
        line-height: 19px;
        &.ant-btn-primary {
          color: #ffffff;
          background: #d92d20;
          border: 1px solid #e63946;
        }
      }
    }
  }
}
.ant-select-selector {
  border-radius: 4px !important;
  .ant-select-prefix {
    margin-right: 8px;
  }
}
.ant-btn {
  border-radius: 8px;
}
.ant-input {
  border-radius: 8px;
}
.ant-form-vertical
  .ant-form-item:not(.ant-form-item-horizontal)
  .ant-form-item-label {
  padding-bottom: 6px;
}
.ant-table-middle {
  .ant-table-column-has-sorters {
    .ant-table-column-title {
      flex: none;
    }
    .ant-table-column-sorter {
      margin-inline-start: 6px;
    }
    .ant-table-column-sorters {
      justify-content: start;
    }
  }
  .ant-table-thead > tr > th {
    padding: 12px 24px !important;
  }
}
/* Modal全局暗黑模式样式 */
html[data-theme="dark"] {
  .ant-picker {
    border-color: #333741;
    background: transparent;
  }

  .ant-select:not(.ant-select-status-error) {
    .ant-select-selector {
      border-color: #333741;
    }
  }
  .ant-select-outlined.ant-select-status-error:not(.ant-select-customize-input)
    .ant-select-selector {
    border-color: #dc4446;
    background: #0c111d;
  }

  .ant-select-outlined:not(.ant-select-customize-input, .ant-select-disabled)
    .ant-select-selector {
    background: transparent;
  }
  .ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector {
    border: 1px solid #333741;
  }
  .ant-picker-panel-container {
    background: var(--antd-modal-bg);
    .ant-picker-header,
    .ant-picker-footer {
      border-color: var(--antd-modal-border);
    }
  }
  .ant-picker-time-panel,
  .ant-picker-dropdown .ant-picker-datetime-panel .ant-picker-time-panel {
    border-inline-start: 1px solid #333741;
  }
  .ant-picker-time-panel-column:not(:first-child) {
    border-color: #333741;
  }
  .ant-btn {
    &:hover {
      color: #f5f5f6 !important;
    }
  }
  .ant-input:not(
      .ant-input-status-error,
      .ant-input-disabled
    ).ant-input-outlined {
    background: transparent;
  }
  .ant-input:not(.ant-input-status-error).ant-input-outlined {
    border-color: #333741;
  }
  .ant-modal-confirm {
    .ant-modal-content {
      background-color: var(--antd-modal-bg);
      border: 1px solid var(--antd-modal-border);
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    }

    .ant-modal-confirm-title {
      display: inline-block;
      width: 100%;
      height: 44px;
      color: var(--antd-modal-title);
    }

    .ant-modal-confirm-content {
      font-weight: 400;
      font-size: 18px;
      line-height: 21px;

      color: #cecfd2;
    }
  }
  .ant-select-dropdown {
    background-color: #1f242f;
  }
  .ant-table-middle {
    .ant-table-thead {
      tr > th {
        padding: 15px 24px;
        &::before {
          background-color: transparent !important;
        }
      }
      tr > th.ant-table-column-has-sorters {
        padding: 12px 24px;
      }
    }

    .ant-table-thead > tr > th {
      background-color: #161b26;
      color: #94969c;
      border-bottom: 1px solid #2a2f3a;
      font-weight: 500;
      font-size: 12px;
      line-height: 14px;
    }
    .ant-table-tbody {
      > tr > td.ant-table-cell {
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
        color: #f5f5f6;
        padding: 28px 24px;
        border-bottom: 1px solid #2a2f3a;
      }
    }

    .ant-table-tbody > tr {
      &:hover {
        td {
          background-color: rgba(31, 36, 47, 0.8) !important;
        }
      }

      .ant-table-tbody > tr:last-child > td {
        border-bottom: none;
      }
    }
  }
  .export-button {
    display: flex;
    align-items: center;
    gap: 8px;
    border: 1px solid #333741;
    border-radius: 8px;
    color: #f5f5f6;

    &:hover {
      background: none;
      color: #f5f5f6;
    }
  }
}

/* 基本样式重置 */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

#root {
  width: 100%;
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

html[data-theme="dark"] ::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

html[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
}

html[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

@media (max-width: 980px) {
  .common-page-header-flex {
    margin: 0 16px;
    flex-direction: column !important;
    justify-content: flex-start !important;
    align-items: flex-start !important;
    gap: 16px;
    .ant-btn {
      width: 100% !important;
    }
  }
  .ant-modal-content {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }
  .ant-table-wrapper {
    overflow: auto;
  }
}
