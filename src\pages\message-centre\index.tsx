import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, List, Badge, Divider, message } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import styles from "./style.module.less";
import AddMessageModal from "./AddMessageModal";
import type { AddMessageFormValues } from "./AddMessageModal";
import { createEvent, getEventList } from "../../services/messageCentre";
import {
  EventEnum,
  type EventType,
  type MessageCentreData,
} from "../../types/MessageCentre";
import { getRelativeDate } from "../../utils/utils";
import alertIcon from "../../assets/message-center/alert-icon.png";
import warningIcon from "../../assets/message-center/warning-icon.png";
import infoIcon from "../../assets/message-center/info-icon.png";
import dayjs from "dayjs";
import { EventMessageEnumMap } from "../../constant/common";
import MessageDetail from "./MessageDetail";
import { useSelector } from "react-redux";
import { store, type RootState } from "../../store";
import { setMessageDetail } from "../../store/message/slice";
import { useNavigate } from "react-router-dom";
import useMobile from "../../hooks/useMobile";

const MessageImage = {
  [EventEnum.Warning]: warningIcon,
  [EventEnum.Alert]: alertIcon,
  [EventEnum.Severely]: alertIcon,
  [EventEnum.Normal]: infoIcon,
  [EventEnum.CloseThePosition]: infoIcon,
  [EventEnum.Info]: infoIcon,
};
const { Title } = Typography;

const MessageCentre: React.FC = () => {
  const isMobile = useMobile();
  const navigate = useNavigate();
  // 模拟消息数据
  const [messages, setMessages] = useState<MessageCentreData[]>([]);

  const [activeTab, setActiveTab] = useState<EventType | "all">("all");
  const [selectedMessage, setSelectedMessage] =
    useState<MessageCentreData | null>(messages[0]);

  const [isAddMessageModalVisible, setIsAddMessageModalVisible] =
    useState<boolean>(false);

  const fetchMessages = async () => {
    const response = await getEventList({});
    setMessages(response);
  };
  useEffect(() => {
    fetchMessages();
  }, []);

  const filteredMessages =
    activeTab === "all"
      ? messages
      : messages.filter((msg) => msg.event_type === activeTab);

  // 分组
  const groupedMessages = filteredMessages.reduce<
    Record<string, MessageCentreData[]>
  >((acc, message) => {
    const date = getRelativeDate(message.create_data);
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(message);
    return acc;
  }, {});

  const handlePublishNewMessage = () => {
    setIsAddMessageModalVisible(true);
  };

  const handleAddMessageCancel = () => {
    setIsAddMessageModalVisible(false);
  };

  const handleAddMessageSubmit = (values: AddMessageFormValues) => {
    setIsAddMessageModalVisible(false);
    fetchMessages();
  };

  const handleMessageClick = (message: MessageCentreData) => {
    setSelectedMessage(message);
    if (isMobile) {
      store.dispatch(setMessageDetail(message));
      navigate(`/message-centre/${message.id}`);
    }
  };

  return (
    <div className={styles.messageCentrePage}>
      <Title level={1} className={styles.pageTitle}>
        Message Centre
      </Title>

      <div className={`${styles.header} common-page-header-flex`}>
        <div className={styles.filterButtons}>
          <Button
            type={activeTab === "all" ? "primary" : "default"}
            className={styles.filterButton}
            onClick={() => setActiveTab("all")}
          >
            All
          </Button>
          {Object.values(EventEnum).map((type) => (
            <Button
              key={type}
              type={activeTab === type ? "primary" : "default"}
              className={styles.filterButton}
              onClick={() => setActiveTab(type)}
            >
              {EventMessageEnumMap[type]}
            </Button>
          ))}
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          className={styles.publishButton}
          onClick={handlePublishNewMessage}
        >
          Publish New Message
        </Button>
      </div>

      <div className={styles.contentContainer}>
        <div className={styles.messageList}>
          {Object.entries(groupedMessages).map(([date, dateMessages], i) => (
            <div
              key={date}
              className={styles.dateGroup}
              style={{ marginTop: i === 0 ? 0 : 24 }}
            >
              <div className={styles.dateHeader}>{date}</div>
              <List
                dataSource={dateMessages}
                renderItem={(message, index) => (
                  <>
                    <List.Item
                      className={`${styles.messageItem} ${
                        selectedMessage?.id === message.id
                          ? styles.activeMessage
                          : ""
                      }`}
                      onClick={() => handleMessageClick(message)}
                    >
                      <div className={styles.messageTime}>
                        {dayjs(message.create_data).format("HH:mm A")}
                      </div>
                      <div className={styles.messageContent}>
                        <img
                          src={MessageImage[message.event_type]}
                          className={`${styles.messageBadge} ${styles.warningBadge}`}
                        />
                        <div className={styles.messageContentRight}>
                          <div className={styles.messageTitle}>
                            {message.event_title}
                          </div>
                          <div className={styles.messageDescription}>
                            {message.event_msg}
                          </div>
                        </div>
                      </div>
                    </List.Item>
                    {index !== dateMessages.length - 1 && (
                      <Divider className={styles.divider} />
                    )}
                  </>
                )}
              />
            </div>
          ))}
        </div>
        {!isMobile && <MessageDetail message={selectedMessage} />}
        {/* <div className={styles.messageDetail}>
          {selectedMessage && (
            <>
              <div className={styles.detailHeader}>
                <div className={styles.detailDate}>
                  {dayjs(selectedMessage.create_data).format(
                    "D MMMM YYYY HH:mm A"
                  )}
                </div>
                <div className={styles.detailTitle}>
                  {selectedMessage.event_title}
                </div>
              </div>
              <div className={styles.detailContent}>
                {selectedMessage.event_msg}
              </div>
            </>
          )}
        </div> */}
      </div>

      <AddMessageModal
        visible={isAddMessageModalVisible}
        onCancel={handleAddMessageCancel}
        onOk={handleAddMessageSubmit}
      />
    </div>
  );
};

export default MessageCentre;
