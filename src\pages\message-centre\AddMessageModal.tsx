import React, { useEffect } from "react";
import { Modal, Form, Input, Select, Button, message } from "antd";
import styles from "./AddMessageModal.module.less";
import { EventEnum } from "../../types/MessageCentre";
import type { EventType } from "../../types/MessageCentre";
import { EventMessageEnumMap } from "../../constant/common";
import { createEvent } from "../../services/messageCentre";

interface AddMessageModalProps {
  params?: {
    project_id: number;
    [key: string]: any;
  };
  visible: boolean;
  eventType?: EventType;
  disabledEventType?: boolean;
  onCancel: () => void;
  onOk: (values: AddMessageFormValues) => void;
}

export interface AddMessageFormValues {
  event_type: EventType;
  event_title: string;
  event_msg: string;
}

const { TextArea } = Input;
const { Option } = Select;

const AddMessageModal: React.FC<AddMessageModalProps> = ({
  params,
  visible,
  eventType,
  disabledEventType,
  onCancel,
  onOk,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);

  useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [visible]);

  const handleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        setLoading(true);
        createEvent({
          ...values,
          ...params,
        })
          .then(() => {
            message.success(
              `Message "${values.event_title}" published successfully`
            );
            onOk(values);
          })
          .finally(() => {
            setLoading(false);
          });
      })
      .catch((info) => {
        console.log("Validation failed:", info);
      });
  };

  return (
    <Modal
      title="Publish New Message"
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={974}
      className={styles.addMessageModal}
      maskClosable={false}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        name="addMessageForm"
        preserve={false}
      >
        <Form.Item
          name="event_type"
          label="Message Type"
          rules={[{ required: true, message: "Please select message type" }]}
          initialValue={eventType}
        >
          <Select
            placeholder="Select message type"
            disabled={disabledEventType}
          >
            {Object.entries(EventEnum).map(([key, value]) => (
              <Option key={key} value={value}>
                {EventMessageEnumMap[value]}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="event_title"
          label="Message Title"
          rules={[{ required: true, message: "Please enter message title" }]}
        >
          <Input placeholder="Enter message title" />
        </Form.Item>

        <Form.Item
          name="event_msg"
          label="Message Content"
          rules={[{ required: true, message: "Please enter message content" }]}
        >
          <TextArea
            placeholder="Enter message content"
            className={styles.textArea}
          />
        </Form.Item>

        <div className={styles.modalFooter}>
          <Button className={styles.cancelButton} onClick={onCancel}>
            Cancel
          </Button>
          <Button
            type="primary"
            className={styles.submitButton}
            onClick={handleSubmit}
            loading={loading}
          >
            Publish
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default AddMessageModal;
