.container {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h2 {
      margin-bottom: 0;
    }
  }
  .actions {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .searchInput {
      width: 240px;
    }

    .exportButton {
      display: flex;
      align-items: center;
    }
  }

  .table {
    margin-top: 24px;
    :global {
      .ant-table-tbody {
        .ant-table-cell {
          padding: 19px 24px !important;
        }
      }
    }
    .actionCell {
      display: flex;
      gap: 12px;
    }
    .stockProjectName {
      font-weight: 400;
      font-size: 14px;
      line-height: 16px;
      color: #f5f5f6;
      min-height: 16px;
    }
    .stockProjectCode {
      margin-top: 2px;
      font-weight: 400;
      font-size: 14px;
      line-height: 16px;
      min-height: 16px;
      color: #94969c;
    }
  }
}
