.dashboardCard {
  width: 100%;
  height: 278px;
  border: 1px solid #333741;
  border-radius: 6px;
  @media (max-width: 681px) {
    height: auto;
  }
  :global {
    .ant-card-head {
      min-height: 23px;
      border-bottom: 0;
      padding: 20px;
      padding-bottom: 0;
      .ant-card-head-title {
        font-weight: 600;
        font-size: 20px;
        line-height: 23px;
        color: #f5f5f6;
      }
    }
    .ant-card-body {
      padding: 24px 20px 10px;
    }
    @media (max-width: 681px) {
      .ant-card-body {
        padding: 24px 20px 4px;
      }
    }
  }
}
.riskOverviewCard {
  .riskOverviewCardContent {
    display: flex;
    .progressContainer {
      flex: 1;
      .chartContainer {
        width: 100%;
        height: 200px;
      }
    }

    .descriptionContainer {
      flex: 1;
      border-left: 1px solid #333741;
      padding: 0 16px;
      .descriptionItem {
        margin-bottom: 16px;
        &Label {
          font-weight: 400;
          font-size: 12px;
          line-height: 14px;
          color: #94969c;
          padding-bottom: 4px;
        }
        &Value {
          font-weight: 600;
          font-size: 20px;
          line-height: 23px;
          color: #f5f5f6;
        }
        &Flex {
          display: flex;
        }
        &RiskCase {
          margin-left: 12px;
        }
      }
    }
    @media (max-width: 681px) {
      flex-direction: column;
      .progressContainer {
        .chartContainer {
          height: 192px;
        }
      }
      .descriptionContainer {
        border-left: 0;
        margin-top: 24px;
        padding: 0;
      }
    }
  }
}

.eventTimelineCard {
  overflow: hidden;
  :global {
    .ant-card-body {
      width: 100%;
      height: calc(100% - 52px);
      overflow-x: hidden;
      overflow-y: auto;
    }
  }
  .eventTimelineAvatar {
    width: 120px;
    display: flex;
    align-items: center;
    .eventTimelineAvatarDotContainer {
      width: 38px;
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .eventTimelineAvatarText {
      margin-left: 4px;
      font-weight: 400;
      font-size: 12px;
      line-height: 14px;
      color: #f5f5f6;
      .eventTimelineAvatarDate {
        margin-bottom: 4px;
      }
    }
  }
  :global {
    .ant-list-item {
      border-block-end: 1px solid #333741;
    }
    .ant-list-item-meta {
      .ant-list-item-meta-title {
        font-weight: 600 !important;
        font-size: 14px !important;
        line-height: 16px !important;
        color: #f5f5f6 !important;
      }
      .ant-list-item-meta-description {
        font-weight: 400 !important;
        font-size: 14px !important;
        line-height: 16px !important;
        color: #94969c !important;
        height: 16px;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.pledgedProjectsSection {
  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    .sectionHeaderTitle {
      font-weight: 600;
      font-size: 26px;
      line-height: 30px;
      color: #f5f5f6;
    }
    .refreshButton {
      font-weight: 500;
      font-size: 14px;
      line-height: 16px;
      color: #f5f5f6;
      .refreshButtonIcon {
        font-size: 14px;
        color: #94969c;
      }
    }
  }
}
