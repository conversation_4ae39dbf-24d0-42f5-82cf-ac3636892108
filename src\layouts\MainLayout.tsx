import { useEffect, useState } from "react";
import {
  Layout,
  Menu,
  theme,
  <PERSON><PERSON>,
  Divider,
  Modal,
  message,
  Select,
} from "antd";
import { Navigate, Outlet, useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  ProjectOutlined,
  Bar<PERSON><PERSON>Outlined,
  MessageOutlined,
  FileTextOutlined,
  TranslationOutlined,
  BulbOutlined,
  LogoutOutlined,
  UserOutlined,
  CloseOutlined,
  OpenAIOutlined,
} from "@ant-design/icons";
import Logo from "../assets/Logo.svg";
import { LOGIN_TOKEN, USER_ROLE_MAP } from "../constant/login";
import styles from "./index.module.less";
import { useSelector } from "react-redux";
import type { RootState } from "../store";
import Avatar from "../components/Avatar";
import usePermission from "../hooks/usePermission";
import { UserRole } from "../types/User";
import Icon from "../components/Icon";
import useMobile from "../hooks/useMobile";

const { Header, Sider, Content } = Layout;

const MainLayout: React.FC = () => {
  const isMobile = useMobile(980);
  const { userInfo } = useSelector((state: RootState) => state.user);
  const { hasPermission } = usePermission();
  const [collapsed, setCollapsed] = useState(isMobile);
  const navigate = useNavigate();
  const location = useLocation();
  const { t, i18n } = useTranslation();
  const { token } = theme.useToken();

  useEffect(() => {
    if (isMobile) {
      setCollapsed(true);
    } else {
      setCollapsed(false);
    }
  }, [isMobile]);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  const changeLanguage = () => {
    const nextLang = i18n.language === "en" ? "zh" : "en";
    i18n.changeLanguage(nextLang);
  };

  const toggleTheme = () => {
    const htmlElement = document.documentElement;
    const currentTheme = htmlElement.getAttribute("data-theme");
    const nextTheme = currentTheme === "dark" ? "light" : "dark";
    htmlElement.setAttribute("data-theme", nextTheme);
    localStorage.setItem("theme", nextTheme);
  };

  // 处理登出
  const handleLogout = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isMobile) {
      setCollapsed(true);
    }
    Modal.confirm({
      width: 480,
      title: t("user.logout"),
      content: t("user.logoutConfirm"),
      icon: null, // 移除默认图标
      closable: true,
      closeIcon: <CloseOutlined style={{ color: "#94969C" }} />,
      centered: true,
      okText: t("user.logout"),
      cancelText: "Cancel",
      className: styles.logoutModal,
      onOk() {
        // 清除登录token
        localStorage.removeItem(LOGIN_TOKEN);
        message.success(t("user.logoutSuccess"));
        // 跳转到登录页
        navigate("/login");
      },
    });
  };

  let pathName = location.pathname.split("/")[1];
  if (pathName === "pledged-detail") {
    if (hasPermission([UserRole.Regulator])) {
      pathName = "pledged-projects";
    } else {
      pathName = "dashboard";
    }
  }

  const handleNavigate = (path: string) => {
    navigate(path);
    if (isMobile) {
      setCollapsed(true);
    }
  };

  const menuItems = [
    {
      key: "dashboard",
      icon: (
        <Icon
          name="dashboard"
          size={16}
          color={pathName === "dashboard" ? "#fff" : "#94969C"}
        />
      ),
      label: t("menu.dashboard"),
      onClick: () => handleNavigate("/dashboard"),
    },
    {
      key: "risk-simulation",
      icon: (
        <Icon
          name="risk-simulation"
          size={16}
          color={pathName === "risk-simulation" ? "#fff" : "#94969C"}
        />
      ),
      label: t("menu.riskSimulation"),
      onClick: () => handleNavigate("/risk-simulation"),
    },
    {
      key: "message-centre",
      icon: (
        <Icon
          name="message-centre"
          size={16}
          color={pathName === "message-centre" ? "#fff" : "#94969C"}
        />
      ),
      label: t("menu.messageCentre"),
      onClick: () => handleNavigate("/message-centre"),
    },
    ...(hasPermission([UserRole.Regulator])
      ? [
          {
            key: "pledged-projects",
            icon: (
              <Icon
                name="pledged-projects"
                size={16}
                color={pathName === "pledged-projects" ? "#fff" : "#94969C"}
              />
            ),
            label: t("menu.pledgedProjects"),
            onClick: () => handleNavigate("/pledged-projects"),
          },
        ]
      : []),
    {
      key: "report",
      icon: (
        <Icon
          name="report"
          size={16}
          color={pathName === "report" ? "#fff" : "#94969C"}
        />
      ),
      label: t("menu.report"),
      onClick: () => handleNavigate("/report"),
    },
    ...(hasPermission([UserRole.Regulator])
      ? [
          {
            key: "manage-users",
            icon: (
              <Icon
                name="add-users"
                size={16}
                color={pathName === "manage-users" ? "#fff" : "#94969C"}
              />
            ),
            label: t("menu.manageUsers"),
            onClick: () => handleNavigate("/manage-users"),
          },
        ]
      : []),
    ...(hasPermission([UserRole.Regulator])
      ? [
          {
            key: "ai-stock-analysis",
            icon: (
              <OpenAIOutlined
                color={pathName === "ai-stock-analysis" ? "#fff" : "#94969C"}
              />
              // <Icon
              //   name="add-users"
              //   size={16}
              //   color={pathName === "ai-stock-analysis" ? "#fff" : "#94969C"}
              // />
            ),
            label: t("menu.aiStockAnalysis"),
            onClick: () => handleNavigate("/ai-stock-analysis"),
          },
        ]
      : []),
  ];

  const handleLayoutClick = () => {
    if (isMobile && !collapsed) {
      setCollapsed(true);
    }
  };

  return (
    <Layout className={styles.layout}>
      <Sider
        className={styles.sider}
        trigger={null}
        collapsible
        theme="dark"
        width={!collapsed ? 280 : 0}
      >
        <div className={styles.logo}>
          <img className={styles.logo} src={Logo} alt="Logo" />
        </div>
        <Menu
          theme="dark"
          mode="inline"
          className={styles.menu}
          selectedKeys={[pathName]}
          items={menuItems}
        />

        {/* 用户信息和登出按钮 */}
        {!collapsed && (
          <div className={styles.userProfile}>
            <div
              className={styles.avatarContainer}
              onClick={() => handleNavigate("/profile")}
              style={{ cursor: "pointer" }}
            >
              <Avatar name={userInfo?.username} />
              <div className={styles.userInfo}>
                <p className={styles.userName}>{userInfo?.username}</p>
                <p className={styles.userRole}>
                  {userInfo?.role && USER_ROLE_MAP[userInfo?.role]}
                </p>
              </div>
            </div>
            <Button
              icon={
                <Icon
                  name="logout"
                  size={16}
                  color="#94969C"
                  style={{ marginTop: 4 }}
                />
              }
              onClick={handleLogout}
              className={styles.logoutButton}
            >
              {t("user.logout")}
            </Button>
          </div>
        )}
      </Sider>
      <Layout
        style={{
          marginLeft: !isMobile ? (collapsed ? 0 : 280) : 0,
          transition: "all 0.2s",
          background: token.colorBgContainer,
        }}
        onClick={handleLayoutClick}
      >
        <Header
          className={styles.header}
          style={{
            background: token.colorBgContainer,
          }}
        >
          <Button
            type="text"
            icon={
              <Icon
                name="expand"
                size={16}
                color={collapsed ? "#94969C" : "#fff"}
              />
            }
            onClick={toggleCollapsed}
            style={{
              fontSize: "16px",
              width: 32,
              height: 32,
            }}
          />
          <div
            style={{
              display: "flex",
              alignItems: "center",
            }}
          >
            <Select
              defaultValue="en"
              onChange={changeLanguage}
              prefix={<Icon name="language" size={16} color="#94969C" />}
            >
              <Select.Option value="en">EN</Select.Option>
              {/* <Select.Option value="zh">中文</Select.Option> */}
            </Select>
            {/* <Button type="text" icon={<BulbOutlined />} onClick={toggleTheme}>
              {t("header.toggleTheme")}
            </Button> */}
          </div>
        </Header>
        <Content
          className={styles.content}
          style={{
            background: token.colorBgContainer,
            borderRadius: token.borderRadiusLG,
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
