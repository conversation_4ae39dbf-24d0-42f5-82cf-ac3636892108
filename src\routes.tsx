import React from "react";
import { createBrowserRouter, Navigate } from "react-router-dom";
import MainLayout from "./layouts/MainLayout";
import Dashboard from "./pages/dashboard";
import Login from "./pages/login";
import AuthRoute from "./components/AuthRoute";
import AuthGuard from "./components/AuthGuard";
import { UserRole } from "./types/User";
import { Spin } from "antd";
import MessageDetail from "./pages/message-centre/MessageDetail";
import RiskSimulationDetail from "./pages/risk-simulation/Detail";

const RiskSimulation = React.lazy(() => import("./pages/risk-simulation"));
const MessageCentre = React.lazy(() => import("./pages/message-centre"));
const PledgedProjects = React.lazy(() => import("./pages/pledged-projects"));
const PledgedDetail = React.lazy(() => import("./pages/pledged-detail"));
const Report = React.lazy(() => import("./pages/report"));
const Profile = React.lazy(() => import("./pages/profile"));
const ManageUsers = React.lazy(() => import("./pages/manage-users"));
const AiStockAnalysis = React.lazy(() => import("./pages/ai-stock-analysis"));
const AiShareholderInfo = React.lazy(
  () => import("./pages/ai-shareholder-info")
);

const router = createBrowserRouter([
  {
    path: "/",
    element: (
      <AuthRoute>
        <MainLayout />
      </AuthRoute>
    ),
    children: [
      {
        index: true,
        element: <Navigate to="/dashboard" replace />,
      },
      {
        path: "dashboard",
        element: <Dashboard />,
      },
      {
        path: "risk-simulation",
        element: (
          <React.Suspense fallback={<Spin />}>
            <RiskSimulation />
          </React.Suspense>
        ),
      },
      {
        path: "risk-simulation/:id",
        element: (
          <React.Suspense fallback={<Spin />}>
            <RiskSimulationDetail />
          </React.Suspense>
        ),
      },
      {
        path: "message-centre",
        element: (
          <React.Suspense fallback={<Spin />}>
            <MessageCentre />
          </React.Suspense>
        ),
      },
      {
        path: "message-centre/:id",
        element: (
          <React.Suspense fallback={<Spin />}>
            <MessageDetail />
          </React.Suspense>
        ),
      },
      {
        path: "pledged-projects",
        element: (
          <React.Suspense fallback={<Spin />}>
            <AuthGuard
              allowedRoles={UserRole.Regulator}
              noPermissionNode={<Navigate to="/" replace />}
            >
              <PledgedProjects />
            </AuthGuard>
          </React.Suspense>
        ),
      },
      {
        path: "pledged-detail/:id",
        element: (
          <React.Suspense fallback={<Spin />}>
            <PledgedDetail />
          </React.Suspense>
        ),
      },
      {
        path: "report",
        element: (
          <React.Suspense fallback={<Spin />}>
            <Report />
          </React.Suspense>
        ),
      },
      {
        path: "profile",
        element: (
          <React.Suspense fallback={<Spin />}>
            <Profile />
          </React.Suspense>
        ),
      },
      {
        path: "manage-users",
        element: (
          <React.Suspense fallback={<Spin />}>
            <AuthGuard
              allowedRoles={UserRole.Regulator}
              noPermissionNode={<Navigate to="/" replace />}
            >
              <ManageUsers />
            </AuthGuard>
          </React.Suspense>
        ),
      },
      {
        path: "ai-stock-analysis",
        element: (
          <React.Suspense fallback={<Spin />}>
            <AuthGuard
              allowedRoles={UserRole.Regulator}
              noPermissionNode={<Navigate to="/" replace />}
            >
              <AiStockAnalysis />
            </AuthGuard>
          </React.Suspense>
        ),
      },
      {
        path: "ai-shareholder-info",
        element: (
          <React.Suspense fallback={<Spin />}>
            <AuthGuard
              allowedRoles={UserRole.Regulator}
              noPermissionNode={<Navigate to="/" replace />}
            >
              <AiShareholderInfo />
            </AuthGuard>
          </React.Suspense>
        ),
      },
    ],
  },
  {
    path: "/login", // 将登录页面移出 MainLayout 范围
    element: <Login />,
  },
]);

export default router;
