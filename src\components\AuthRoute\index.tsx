import React, { useEffect, useState } from "react";
import { Spin } from "antd";
import { Navigate, useLocation } from "react-router-dom";
import { LOGIN_TOKEN } from "../../constant/login";
import { useAppSelector } from "../../hooks/useStore";
import { getCurrentUser } from "../../services/auth";
import { useDispatch } from "react-redux";
import { setLoading, setUserInfo } from "../../store/user/slice";

const AuthRoute = ({ children }: { children: React.ReactNode }) => {
  const dispatch = useDispatch();
  const { userInfo, loading } = useAppSelector((state) => state.user);
  const token = localStorage.getItem(LOGIN_TOKEN);
  const location = useLocation();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  useEffect(() => {
    const checkAuth = async () => {
      if (!token) {
        setIsAuthenticated(false);
        dispatch(setLoading(false));
        return;
      }

      if (!userInfo) {
        try {
          dispatch(setLoading(true));
          const userData = await getCurrentUser();
          dispatch(setUserInfo(userData));
          setIsAuthenticated(true);
        } catch (error) {
          setIsAuthenticated(false);
          localStorage.removeItem(LOGIN_TOKEN);
        } finally {
          dispatch(setLoading(false));
        }
      } else {
        setIsAuthenticated(true);
        dispatch(setLoading(false));
      }
    };

    checkAuth();
  }, [token, userInfo, dispatch]);

  if (loading || isAuthenticated === null) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          width: "100vw",
          height: "100vh",
        }}
      >
        <Spin />
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace state={{ from: location }} />;
  }

  return <>{children}</>;
};

export default AuthRoute;
