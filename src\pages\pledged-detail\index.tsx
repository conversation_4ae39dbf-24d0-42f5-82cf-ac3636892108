import React, { useEffect, useRef, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Button,
  Card,
  Tag,
  Select,
  Modal,
  message,
  Typography,
  Upload,
  type UploadProps,
} from "antd";

import {
  EditOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  CloseOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import PageHeader from "../../components/PageHeader";
import styles from "./style.module.less";
import AuthGuard from "../../components/AuthGuard";
import { UserRole } from "../../types/User";
import {
  deletePledgeProject,
  getProjectById,
} from "../../services/pledgedProjects";
import type {
  PledgedProject,
  PledgedProjectLender,
  RiskLevel,
} from "../../types/pledgedProject";
import dayjs from "dayjs";
import {
  calculateTimeLeft,
  getRiskLevelColor,
  getRiskLevelText,
} from "../../utils/utils";
import { EventEnum } from "../../types/MessageCentre";
import AddMessageModal, {
  type AddMessageFormValues,
} from "../message-centre/AddMessageModal";
import AddOrEditProjectModal from "../pledged-projects/AddOrEditProjectModal";
import Chart from "./Chart";
import Icon from "../../components/Icon";
import { PledgedProjectTagColors } from "../../constant/common";
import NewsModal from "./NewsModal";
import { LOGIN_TOKEN } from "../../constant/login";
import SHInfoModal from "./SHInfoModal";

const { Title } = Typography;

const PledgedDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [detail, setDetail] = useState<PledgedProject | null>(null);
  const [lenders, setLenders] = useState<PledgedProjectLender[]>([]);
  const [borrower, setBorrower] = useState<{
    borrower_id: number;
    borrower_name: string;
  }>();
  const [isEditModalVisible, setIsEditModalVisible] = useState<boolean>(false);
  const [isNewsModalVisible, setIsNewsModalVisible] = useState<boolean>(false);
  const [isAddMessageModalVisible, setIsAddMessageModalVisible] =
    useState<boolean>(false);
  const [currentProject, setCurrentProject] = useState<PledgedProject | null>(
    null
  );
  const [isSHInfoModalVisible, setIsSHInfoModalVisible] =
    useState<boolean>(false);
  const [uploadPdfLoading, setUploadPdfLoading] = useState<boolean>(false);

  const getDetail = () => {
    if (id) {
      getProjectById({
        project_id: id,
      }).then((res) => {
        setDetail(res.project.project);
        setLenders(res.project.lenders);
        setBorrower(res.project.borrower);
      });
    }
  };

  useEffect(() => {
    if (id) {
      getDetail();
    }
  }, [id]);

  if (!detail) {
    return null;
  }

  const isPriceUp = parseFloat(detail.change_percent) > 0;

  const handleDelete = () => {
    Modal.confirm({
      width: 480,
      title: "Delete Pledged Asset",
      content:
        "Are you sure you want to delete this pledged asset? This action cannot be undone.",
      okText: "Delete Asset",
      icon: null,
      closable: true,
      closeIcon: <CloseOutlined style={{ color: "#94969C" }} />,
      centered: true,
      cancelText: "Cancel",
      onOk: () => {
        deletePledgeProject({ project_id: detail.project_id }).then(() => {
          navigate(-1);
          message.success("项目已删除");
        });
      },
    });
  };

  const handleEditProject = () => {
    setIsEditModalVisible(true);
    setCurrentProject({
      ...detail,
      lenders,
      borrower,
    });
  };

  const renderAction = () => {
    return (
      <AuthGuard allowedRoles={UserRole.Regulator}>
        <div className={styles.actionSection}>
          <Button
            color="default"
            variant="solid"
            ghost
            icon={<EditOutlined />}
            className={styles.editButton}
            onClick={handleEditProject}
          >
            Edit
          </Button>
          <Button
            type="primary"
            danger
            icon={<DeleteOutlined />}
            className={styles.deleteButton}
            onClick={handleDelete}
          >
            Delete
          </Button>
        </div>
      </AuthGuard>
    );
  };

  const handlePublishNewMessage = () => {
    setIsAddMessageModalVisible(true);
  };

  const handlePublishNewNews = () => {
    setIsNewsModalVisible(true);
  };

  const handleAddMessageCancel = () => {
    setIsAddMessageModalVisible(false);
  };

  const handleAddMessageSubmit = (values: AddMessageFormValues) => {
    setIsAddMessageModalVisible(false);
  };

  const handleAddOrEditCancel = () => {
    setIsEditModalVisible(false);
    setCurrentProject(null);
  };

  const handleAddOrEditProject = (values: any) => {
    setIsEditModalVisible(false);
    setCurrentProject(null);
    getDetail();
  };

  const token = localStorage.getItem(LOGIN_TOKEN);
  const uploadPdfProps: UploadProps = {
    name: "pdf_file",
    data: {
      project_id: detail?.project_id,
    },
    action: `${import.meta.env.VITE_API_BASE_URL || "/api"}/pdf/uploadPdf`,
    headers: {
      Authorization: token!,
    },
    accept: "application/pdf",
    showUploadList: false,
    disabled: uploadPdfLoading,
    onChange(info) {
      if (info.file.status !== "uploading") {
        console.log(info.file, info.fileList);
      }
      setUploadPdfLoading(info.file.status === "uploading");
      if (info.file.status === "done") {
        message.success(
          "The upload has been successful. AI is currently analyzing the data. The analysis will be completed in about 1-3 minutes. Please wait."
        );
      } else if (info.file.status === "error") {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
  };

  const handleSHInfo = () => {
    setIsSHInfoModalVisible(true);
  };

  return (
    <div className={styles.pledgedDetailsContainer}>
      <Title level={1} className={styles.title}>
        Pledged Details
      </Title>
      <PageHeader onBack={() => navigate(-1)} rightContent={renderAction()} />

      <div className={styles.basicInfo}>
        <div className={styles.stockInfo}>
          <div className={styles.label}>{detail.stock_code}</div>
          <div className={styles.value}>{detail.stock_name}</div>
        </div>

        <div className={styles.priceInfo}>
          <div className={styles.priceBlock}>
            <div className={styles.label}>Current Price</div>
            <div className={styles.value}>
              {detail.current_stock_price}
              <span
                className={`${styles.priceChange} ${
                  isPriceUp ? styles.priceUp : styles.priceDown
                }`}
              >
                {parseFloat(detail.change_percent) > 0 ? "+" : ""}
                {detail.change_percent}
              </span>
            </div>
          </div>
          <div className={styles.priceBlock}>
            <div className={styles.label}>Pledge Price</div>
            <div className={styles.value}>
              {detail.pledge_price ??
                (
                  Number(detail.stock_price) *
                  (Number(detail.pledge_rate) / 100)
                ).toFixed(2)}
            </div>
          </div>
        </div>
        <div className={styles.uploadContainer}>
          <Upload {...uploadPdfProps}>
            <Button
              loading={uploadPdfLoading}
              type="primary"
              icon={<UploadOutlined />}
            >
              {uploadPdfLoading ? "Uploading..." : "Upload PDF"}
            </Button>
          </Upload>

          <Button
            type="primary"
            className={styles.sendAlertButton}
            onClick={handleSHInfo}
          >
            SH.Info
          </Button>
        </div>
      </div>

      <div className={styles.chartSection}>
        <Chart detail={detail} />

        <div className={styles.infoCardContainer}>
          <div className={styles.sendAlertButtonContainer}>
            <AuthGuard allowedRoles={UserRole.Regulator}>
              <Button
                type="primary"
                className={styles.sendAlertButton}
                onClick={handlePublishNewMessage}
              >
                Send Alert Message
              </Button>
            </AuthGuard>
            <Button
              type="primary"
              className={styles.sendAlertButton}
              onClick={handlePublishNewNews}
            >
              Project News
            </Button>
          </div>
          <Card className={styles.infoCard}>
            <div className={styles.infoCardTitle}>Basic Information</div>

            <div className={styles.infoRow}>
              <div className={styles.infoItem}>
                <div className={styles.infoLabel}>Current Price</div>
                <div className={styles.infoValue}>
                  {detail.current_stock_price}
                </div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.infoLabel}>Required Shares</div>
                <div className={styles.infoValue}>{detail.required_shares}</div>
              </div>
            </div>

            <div className={styles.infoRow}>
              <div className={styles.infoItem}>
                <div className={styles.infoLabel}>Pledged Ratio</div>
                <div className={styles.infoValue}>{detail.pledge_rate}%</div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.infoLabel}>Pledge Start Date</div>
                <div className={styles.infoValue}>
                  {detail.start_date &&
                    dayjs(detail.start_date).format("D MMMM YYYY")}
                </div>
              </div>
            </div>

            <div className={styles.infoRow}>
              <div className={styles.infoItem}>
                <div className={styles.infoLabel}>Stock Price</div>
                <div className={styles.infoValue}>{detail.stock_price}</div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.infoLabel}>Risk Level</div>
                <div className={styles.infoValue}>
                  <Tag
                    color={getRiskLevelColor(
                      typeof detail.safety_status === "string"
                        ? (detail.safety_status?.toLowerCase() as RiskLevel)
                        : (detail.safety_status.line?.toLowerCase() as RiskLevel)
                    )}
                    className={styles.riskTag}
                  >
                    {getRiskLevelText(
                      typeof detail.safety_status === "string"
                        ? (detail.safety_status?.toLowerCase() as RiskLevel)
                        : (detail.safety_status.line?.toLowerCase() as RiskLevel)
                    )}
                  </Tag>
                </div>
              </div>
            </div>

            <div className={styles.infoRow}>
              <div className={styles.infoItem}>
                <div className={styles.thresholdLabelContainer}>
                  <div
                    className={styles.thresholdDot}
                    style={{
                      backgroundColor:
                        PledgedProjectTagColors.critical.backgroundColor,
                    }}
                  ></div>
                  <div className={styles.thresholdLabel}>Warning Settings</div>
                </div>
                <div className={styles.infoValue}>
                  {detail.warninglineconfig}
                </div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.thresholdLabelContainer}>
                  <div
                    className={styles.thresholdDot}
                    style={{
                      backgroundColor:
                        PledgedProjectTagColors.critical.backgroundColor,
                    }}
                  ></div>
                  <div className={styles.thresholdLabel}>Warning Line</div>
                </div>
                <div className={styles.infoValue}>{detail.warning_line}</div>
              </div>
            </div>
            <div className={styles.infoRow}>
              <div className={styles.infoItem}>
                <div className={styles.thresholdLabelContainer}>
                  <div
                    className={styles.thresholdDot}
                    style={{
                      backgroundColor:
                        PledgedProjectTagColors.high.backgroundColor,
                    }}
                  ></div>
                  <div className={styles.thresholdLabel}>Alert Settings</div>
                </div>
                <div className={styles.infoValue}>{detail.alertlineconfig}</div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.thresholdLabelContainer}>
                  <div
                    className={styles.thresholdDot}
                    style={{
                      backgroundColor:
                        PledgedProjectTagColors.high.backgroundColor,
                    }}
                  ></div>
                  <div className={styles.thresholdLabel}>Alert Line</div>
                </div>
                <div className={styles.infoValue}>{detail.alert_line}</div>
              </div>
            </div>
            <div className={styles.infoRow}>
              <div className={styles.infoItem}>
                <div className={styles.thresholdLabelContainer}>
                  <div
                    className={styles.thresholdDot}
                    style={{
                      backgroundColor:
                        PledgedProjectTagColors.medium.backgroundColor,
                    }}
                  ></div>
                  <div className={styles.thresholdLabel}>
                    Liquidation Settings
                  </div>
                </div>
                <div className={styles.infoValue}>
                  {detail.liquidationlineconfig}
                </div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.thresholdLabelContainer}>
                  <div
                    className={styles.thresholdDot}
                    style={{
                      backgroundColor:
                        PledgedProjectTagColors.medium.backgroundColor,
                    }}
                  ></div>
                  <div className={styles.thresholdLabel}>Liquidation Line</div>
                </div>
                <div className={styles.infoValue}>
                  {detail.liquidation_line}
                </div>
              </div>
            </div>
            {lenders.map((lender) => (
              <div className={`${styles.infoRow} ${styles.lender}`}>
                <div className={styles.infoItem}>
                  <div className={styles.infoLabel}>Lender</div>
                  <div className={styles.infoValue}>{lender.lender_name}</div>
                </div>
                <div className={styles.infoItem}>
                  <div className={styles.infoLabel}>Borrowed Amount </div>
                  <div className={styles.infoValue}>
                    {lender.borrowed_amount}
                  </div>
                </div>
              </div>
            ))}
            <div className={`${styles.infoRow} ${styles.lender}`}>
              <div className={styles.infoItem}>
                <div className={styles.infoLabel}>Borrower</div>
                <div className={styles.infoValue}>
                  {borrower?.borrower_name}
                </div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.infoLabel}>
                  Total Project Borrowed Amount{" "}
                </div>
                <div className={styles.infoValue}>
                  {lenders.reduce(
                    (total, lender) => total + Number(lender.borrowed_amount),
                    0
                  )}
                </div>
              </div>
            </div>

            <div className={styles.timeRemaining}>
              <Icon
                name="clock"
                size={16}
                color="#fff"
                className={styles.clockIcon}
              />
              {calculateTimeLeft(detail.start_date, detail.end_date)}
            </div>
          </Card>
        </div>
      </div>
      <AuthGuard allowedRoles={[UserRole.Regulator]}>
        <AddMessageModal
          params={{
            project_id: detail.project_id,
          }}
          visible={isAddMessageModalVisible}
          eventType={EventEnum.Alert}
          // disabledEventType={true}
          onCancel={handleAddMessageCancel}
          onOk={handleAddMessageSubmit}
        />
        <AddOrEditProjectModal
          visible={isEditModalVisible}
          projectData={currentProject}
          onCancel={handleAddOrEditCancel}
          onOk={handleAddOrEditProject}
        />
      </AuthGuard>
      <NewsModal
        visible={isNewsModalVisible}
        onCancel={() => setIsNewsModalVisible(false)}
        stock_code={detail.stock_code}
      />
      <SHInfoModal
        visible={isSHInfoModalVisible}
        onCancel={() => setIsSHInfoModalVisible(false)}
        projectId={detail.project_id}
      />
    </div>
  );
};

export default PledgedDetails;
