import { useEffect, useState } from "react";
import { getUserList } from "../services/auth";
import { UserRole, type UserInfo, type UserRoleType } from "../types/User";

const useBorrowersAndLenders = () => {
  const [borrowers, setBorrowers] = useState<
    Array<{ value: string; label: string }>
  >([]);
  const [lenders, setLenders] = useState<
    Array<{ value: string; label: string }>
  >([]);
  const fetchUserList = () => {
    getUserList().then((data) => {
      setBorrowers(
        data
          .filter((user: UserInfo) => user.role === UserRole.Borrower)
          .map((user: UserInfo) => ({
            value: user.id!,
            label: user.real_name!,
          }))
      );
      setLenders(
        data
          .filter((user: UserInfo) => user.role === UserRole.Lender)
          .map((user: UserInfo) => ({
            value: user.id!,
            label: user.real_name!,
          }))
      );
    });
  };

  useEffect(() => {
    fetchUserList();
  }, []);

  return {
    borrowers,
    lenders,
  };
};

export default useBorrowersAndLenders;
