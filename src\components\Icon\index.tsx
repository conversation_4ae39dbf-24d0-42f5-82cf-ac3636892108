import React from "react";
import type { CSSProperties } from "react";

// 定义一个接口来存储SVG URL
interface IconsMap {
  [key: string]: string;
}

// 存储SVG的URL
const icons: IconsMap = {};

// 导入SVG文件
const importIcons = async () => {
  try {
    // 使用Vite的import.meta.glob动态导入所有SVG文件
    const modules = import.meta.glob("../../assets/icons/*.svg", {
      as: "url", // 作为URL导入
      eager: true,
    });

    // 处理导入的SVG文件
    Object.entries(modules).forEach(([path, url]) => {
      // 从路径中提取图标名称
      const iconName = path.split("/").pop()?.replace(".svg", "");
      if (iconName) {
        icons[iconName] = url as string;
      }
    });
  } catch (error) {
    console.error("Failed to load SVG icons:", error);
  }
};

// 加载图标
importIcons();

export interface IconProps {
  /**
   * 图标名称，对应src/assets/icons文件夹下的SVG文件名（不含.svg后缀）
   */
  name: string;
  /**
   * 图标大小，可以是数字（单位为px）或字符串（如'1em', '24px'等）
   */
  size?: number | string;
  /**
   * 图标颜色
   */
  color?: string;
  /**
   * 自定义样式
   */
  style?: CSSProperties;
  /**
   * 自定义类名
   */
  className?: string;
  /**
   * 点击事件
   */
  onClick?: (e: React.MouseEvent) => void;
}

/**
 * Icon组件 - 用于显示SVG图标
 *
 * 使用方法：
 * ```tsx
 * <Icon name="dashboard" size={24} color="#1890ff" />
 * ```
 */
const Icon: React.FC<IconProps> = ({
  name,
  size = 16,
  color,
  style = {},
  className = "",
  onClick,
  ...rest
}) => {
  const sizeStyle = typeof size === "number" ? `${size}px` : size;

  const iconUrl = icons[name];

  if (!iconUrl) {
    console.warn(`Icon "${name}" not found in src/assets/icons folder`);
    return null;
  }

  // 使用fetch获取SVG内容
  const [svgContent, setSvgContent] = React.useState<string>("");

  React.useEffect(() => {
    // 获取SVG内容
    fetch(iconUrl)
      .then((response) => response.text())
      .then((text) => {
        // 提取SVG的宽度和高度属性
        const widthMatch = text.match(/width="([^"]*)"/i);
        const heightMatch = text.match(/height="([^"]*)"/i);

        // 修改SVG内容
        let modifiedSvg = text;

        // 设置宽度和高度
        if (sizeStyle) {
          if (widthMatch) {
            modifiedSvg = modifiedSvg.replace(
              /width="([^"]*)"/i,
              `width="${sizeStyle}"`
            );
          } else {
            // 如果没有width属性，添加一个
            modifiedSvg = modifiedSvg.replace(
              /<svg/,
              `<svg width="${sizeStyle}"`
            );
          }

          if (heightMatch) {
            modifiedSvg = modifiedSvg.replace(
              /height="([^"]*)"/i,
              `height="${sizeStyle}"`
            );
          } else {
            // 如果没有height属性，添加一个
            modifiedSvg = modifiedSvg.replace(
              /<svg/,
              `<svg height="${sizeStyle}"`
            );
          }
        }

        // 设置颜色
        if (color) {
          if (name !== "expand") {
            modifiedSvg = modifiedSvg.replace(/<svg/, `<svg fill="${color}"`);
          } else {
            modifiedSvg = modifiedSvg.replace(/<svg/, `<svg stroke="${color}"`);
          }
        }

        setSvgContent(modifiedSvg);
      })
      .catch((error) => {
        console.error(`Error loading SVG: ${error}`);
      });
  }, [iconUrl, color, sizeStyle]);

  return (
    <span
      className={`icon ${className}`}
      style={{
        display: "inline-flex",
        alignItems: "center",
        width: size,
        height: size,
        ...style,
      }}
      onClick={onClick}
      dangerouslySetInnerHTML={svgContent ? { __html: svgContent } : undefined}
      {...rest}
    />
  );
};

export default Icon;
