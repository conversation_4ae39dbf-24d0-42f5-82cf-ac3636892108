import React, { useEffect, useRef } from "react";
import * as echarts from "echarts";

export const upColor = "#00da3c";
export const upBorderColor = "#008F28";
export const downColor = "#ec0000";
export const downBorderColor = "#8A0000";

const CandlestickChart: React.FC<{
  name: string;
  data: {
    categoryData: (string | number)[];
    values: (string | number)[][];
    seriesConfig?: any[];
  };
  xAxisLabelRotate?: number;
  markPoint?: any;
  chartType?: "candlestick" | "line";
  grid?: any;
  className?: string;
  onClick?: (value: any) => void;
}> = ({
  name,
  data,
  xAxisLabelRotate,
  chartType = "candlestick",
  grid,
  className,
  markPoint,
  onClick,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      // const periods: { [key: string]: Array<number | string> } = {
      //   day: Array.from({ length: 24 }, (_, i) => i),
      //   week: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      //   month: Array.from({ length: dayjs().daysInMonth() }, (_, i) => i),
      //   months: Array.from({ length: 3 }, (_, i) => i + 1),
      //   year: Array.from({ length: 12 }, (_, i) => i + 1),
      // };
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }

      const chart = echarts.init(chartRef.current);
      chartInstance.current = chart;

      const maxValue = data.values.reduce((max: number, item: any[]) => {
        const childMax = Math.max(...item);
        return Math.max(max, childMax);
      }, 0);
      let option = {};
      if (chartType === "line") {
        const minMarkLine = data.seriesConfig?.reduce(
          (min: number, item: any) => {
            const childMin = Math.min(
              ...item.markLine.data.map((item: any) => item.yAxis)
            );
            return Math.min(min, childMin);
          },
          Number.MAX_VALUE
        );
        const maxMarkLine = data.seriesConfig?.reduce(
          (max: number, item: any) => {
            const childMax = Math.max(
              ...item.markLine.data.map((item: any) => item.yAxis)
            );
            return Math.max(max, childMax);
          },
          Number.MIN_VALUE
        );
        option = {
          darkMode: true,
          grid: {
            // left: "5%",
            left: `54px`,
            // left: `${maxValue.toString().length * 12}px`,
            right: "2%",
            bottom: "60px",
            top: "10px",
            ...grid,
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
            },
            backgroundColor: "#0c111d",
            borderColor: "#1F242F",
          },
          legend: {
            show: false,
            data: [name],
          },
          xAxis: {
            type: "category",
            boundaryGap: false,
            data: data.categoryData,
            nameLocation: "middle",
            axisLabel: {
              rotate: xAxisLabelRotate ?? 40,
            },
          },
          yAxis: {
            type: "value",
            min: function (value: any) {
              const calc = Number(value.min - value.min * 0.1).toFixed(2);
              return minMarkLine && Number(minMarkLine) < Number(calc)
                ? Number(minMarkLine - minMarkLine * 0.01).toFixed(2)
                : calc;
            },
            max: function (value: any) {
              const calc = Number(value.max + value.max * 0.1).toFixed(2);
              return maxMarkLine && Number(maxMarkLine) > Number(calc)
                ? Number(maxMarkLine + maxMarkLine * 0.01).toFixed(2)
                : calc;
            },
            scale: true,
            axisLine: {
              show: false,
            },
            axisLabel: {
              color: "#94969C",
            },
            splitLine: {
              lineStyle: {
                color: "#1F242F",
              },
            },
          },
          series: data.values.map((item: any[], index) => ({
            data: item,
            type: "line",
            smooth: true,
            ...(data.seriesConfig?.[index] || {}),
          })),
        };
      } else {
        option = {
          darkMode: true,
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
            },
            backgroundColor: "#0c111d",
            borderColor: "#1F242F",
          },
          legend: {
            show: false,
            data: [name],
          },
          grid: {
            // left: "5%",
            left: `${
              Number(maxValue + maxValue * 0.1)
                .toFixed(3)
                .toString().length * 12
            }px`,
            right: "3%",
            bottom: "5%",
            top: "2%",
          },
          xAxis: {
            type: "category",
            data: data.categoryData,
            boundaryGap: false,
            axisLine: { onZero: false },
            splitLine: { show: false },
            min: "dataMin",
            max: "dataMax",
          },
          yAxis: {
            min: function (value: any) {
              return Number(value.min - value.min * 0.1).toFixed(3);
            },
            max: function (value: any) {
              return Number(value.max + value.max * 0.1).toFixed(3);
            },
            scale: true,
            axisLine: {
              show: false,
            },
            axisLabel: {
              color: "#94969C",
            },
            splitLine: {
              lineStyle: {
                color: "#1F242F",
              },
            },
          },
          // dataZoom: [
          //   {
          //     type: "inside",
          //     start: 50,
          //     end: 100,
          //   },
          //   {
          //     show: true,
          //     type: "slider",
          //     top: "90%",
          //     start: 50,
          //     end: 100,
          //   },
          // ],
          series: [
            {
              name: name,
              type: "candlestick",
              data: data.values,
              lineStyle: {
                color: "#155EEF",
                width: 1,
              },
              itemStyle: {
                color: upColor,
                color0: downColor,
                borderColor: upBorderColor,
                borderColor0: downBorderColor,
              },
              ...(markPoint ? { markPoint } : {}),
            },
            // {
            //   name: "Warning Line",
            //   type: "line",
            //   data: calculateMA(data0, parseFloat(detail.warning_line)),
            //   smooth: true,
            //   lineStyle: {
            //     opacity: 0.5,
            //   },
            // },
            // {
            //   name: "Alert Line",
            //   type: "line",
            //   data: calculateMA(data0, parseFloat(detail.alert_line)),
            //   smooth: true,
            //   lineStyle: {
            //     opacity: 0.5,
            //   },
            // },
            // {
            //   name: "Liquidation Line",
            //   type: "line",
            //   data: calculateMA(data0, parseFloat(detail.liquidation_line)),
            //   smooth: true,
            //   lineStyle: {
            //     opacity: 0.5,
            //   },
            // },
          ],
        };
      }

      chart.setOption(option);

      // 响应窗口大小变化
      const handleResize = () => {
        chart.resize();
      };
      window.addEventListener("resize", handleResize);
      chart.on("click", (params: any) => {
        onClick?.(params);
      });
      return () => {
        window.removeEventListener("resize", handleResize);
        chart.dispose();
        chartInstance.current = null;
      };
    }
  }, [data.categoryData, data.values]);

  return <div ref={chartRef} className={className}></div>;
};

export default CandlestickChart;
