import { get, post } from "../utils/request";
import type { StockOption, StockReport } from "../types/Report";

export const getReports = (query: any) => {
  return post<{ total: number; data: StockReport[] }>(
    `/Project/getProjectReport`,
    query,
    undefined,
    undefined,
    undefined,
    true
  );
};

export const getStocks = () => {
  return post<StockOption[]>(`/Project/getUserProjects`, {});
};

export const exportReport = (query: any) => {
  return post<{ download_url: string }>(`/Project/exportProjectReport`, query);
};
