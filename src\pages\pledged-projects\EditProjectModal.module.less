.editProjectModal {
  :global {
    .ant-modal-content {
      background-color: #161b26;
      border-radius: 8px;
      padding: 40px;
    }

    .ant-modal-header {
      background-color: #161b26;
      border-bottom: none;
      margin-bottom: 0;

      .ant-modal-title {
        color: #f5f5f6;
        font-weight: 600;
        font-size: 18px;
      }
    }

    .ant-modal-body {
      padding: 0;
      margin-top: 40px;
    }

    .ant-modal-close {
      color: #6c7293;

      &:hover {
        color: #f5f5f6;
      }
    }

    .ant-form-item-label > label {
      color: #f5f5f6;
      font-size: 14px;
    }

    .ant-input {
      border-color: #2a2f3a !important;
      color: #f5f5f6 !important;

      &::placeholder {
        color: #6c7293;
      }
    }

    .ant-form-item-explain-error {
      color: #ff4d4f;
    }
  }
  
  .form {
    margin-top: 16px;
  }
  
  .formActions {
    margin-top: 24px;
    
    .saveButton {
      height: 48px;
      font-size: 16px;
      border-radius: 8px;
    }
  }
}
