import { createSlice, type PayloadAction } from "@reduxjs/toolkit";
import type { UserInfo } from "../../types/User";
interface UserState {
  userInfo: UserInfo | null;
  loading: boolean;
}

const initialState: UserState = {
  userInfo: null,
  loading: false,
};

export const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUserInfo: (state, action: PayloadAction<UserInfo>) => {
      state.userInfo = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    clearUserInfo: (state) => {
      state.userInfo = null;
    },
  },
});

export const { setUserInfo, setLoading, clearUserInfo } = userSlice.actions;

export default userSlice.reducer;
