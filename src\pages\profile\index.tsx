import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { EditOutlined } from "@ant-design/icons";
import { Button, message } from "antd";
import styles from "./index.module.less";
import EmailModal from "./emailModal";
import PhoneModal from "./phoneModal";
import PasswordModal from "./passwordModal";
import { useAppSelector } from "../../hooks/useStore";
import { USER_ROLE_MAP } from "../../constant/login";
import Avatar from "../../components/Avatar";

const Profile: React.FC = () => {
  const { t } = useTranslation();
  const { userInfo } = useAppSelector((state) => state.user);
  const [emailModalVisible, setEmailModalVisible] = useState(false);
  const [phoneModalVisible, setPhoneModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [email, setEmail] = useState(userInfo?.email);
  const [phone, setPhone] = useState(userInfo?.phone);

  useEffect(() => {
    if (userInfo) {
      setEmail(userInfo.email);
      setPhone(userInfo.phone);
    }
  }, [userInfo]);

  // 处理邮箱编辑
  const handleEditEmail = () => {
    setEmailModalVisible(true);
  };

  // 处理手机号编辑
  const handleEditPhone = () => {
    setPhoneModalVisible(true);
  };

  // 处理密码修改
  const handleChangePassword = () => {
    setPasswordModalVisible(true);
  };

  // 保存邮箱
  const handleSaveEmail = (newEmail: string) => {
    setEmail(newEmail);
    setEmailModalVisible(false);
    message.success("Email updated successfully");
  };

  // 保存手机号
  const handleSavePhone = (newPhone: string) => {
    setPhone(newPhone);
    setPhoneModalVisible(false);
    message.success("Mobile number updated successfully");
  };

  // 密码修改成功回调
  const handlePasswordSuccess = () => {
    setPasswordModalVisible(false);
    message.success("Password updated successfully");
  };

  return (
    <div className={styles.profileContainer}>
      <h1 className={styles.pageTitle}>{t("user.profile")}</h1>

      <div className={styles.profileCard}>
        <div className={styles.profileHeader}>
          <Avatar name={userInfo?.username} size={64} />

          <div className={styles.userDetails}>
            <h2 className={styles.userName}>{userInfo?.username}</h2>
            <p className={styles.userRole}>
              {userInfo?.role && USER_ROLE_MAP[userInfo?.role]}
            </p>
          </div>
        </div>

        <div className={styles.profileInfo}>
          <div className={styles.infoItem}>
            <div className={styles.infoValue}>
              <span>{email}</span>
              <EditOutlined
                className={styles.editIcon}
                onClick={handleEditEmail}
              />
            </div>
          </div>

          <div className={styles.infoItem}>
            <div className={styles.infoValue}>
              <span>{phone}</span>
              <EditOutlined
                className={styles.editIcon}
                onClick={handleEditPhone}
              />
            </div>
          </div>

          <div className={styles.passwordSection}>
            <Button
              type="link"
              className={styles.changePasswordBtn}
              onClick={handleChangePassword}
            >
              {t("user.changePassword")}
            </Button>
          </div>
        </div>
      </div>

      <EmailModal
        visible={emailModalVisible}
        onClose={() => setEmailModalVisible(false)}
        onSave={handleSaveEmail}
        initialValue={email || ""}
      />

      <PhoneModal
        visible={phoneModalVisible}
        onClose={() => setPhoneModalVisible(false)}
        onSave={handleSavePhone}
        initialValue={phone || ""}
      />

      <PasswordModal
        visible={passwordModalVisible}
        onClose={() => setPasswordModalVisible(false)}
        onSuccess={handlePasswordSuccess}
      />
    </div>
  );
};

export default Profile;
