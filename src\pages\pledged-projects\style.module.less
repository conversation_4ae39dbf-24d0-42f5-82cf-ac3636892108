.pledgedProjectsPage {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .title {
      font-weight: 700;
      font-size: 28px;
      line-height: 33px;
      color: #ececed;
      margin: 0;
    }

    .addButton {
      display: flex;
      align-items: center;
      gap: 8px;
      height: 40px;
      padding: 0 16px;
      border-radius: 4px;
      font-weight: 500;
    }
  }

  .searchExportRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .searchInput {
      width: 300px;

      :global(.ant-input-prefix) {
        color: #6c7293;
      }
    }

    .exportButton {
      display: flex;
      align-items: center;
      gap: 8px;
      height: 40px;
      padding: 0 16px;
      border-radius: 4px;
      background-color: transparent;
      border: 1px solid #6c7293;
      color: #f5f5f6;

      &:hover {
        border-color: #8c8dfc;
        color: #8c8dfc;
      }
    }
  }

  .table {
    :global {
      .ant-table-tbody {
        .ant-table-cell {
          padding: 19px 24px !important;
        }
      }
    }
    .stockCell {
      display: flex;
      flex-direction: column;

      .stockName {
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
        color: #f5f5f6;
        margin-bottom: 2px;
      }

      .stockId {
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;

        color: #94969c;
      }
    }

    .riskTag {
      border-radius: 6px;
      font-weight: 600;
      font-size: 12px;
      color: #ffffff;
      text-align: center;
      line-height: 14px;
      display: inline-block;
      padding: 4px 8px;

      &.low {
        background-color: #17b26a;
        color: #000;
      }

      &.medium {
        background-color: #f79009;
        color: #000;
      }

      &.high {
        background-color: #f04438;
        color: #fff;
      }

      &.critical {
        background-color: #f04438;
        color: #fff;
      }
    }

    .timeCell {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #f5f5f6;

      .clockIcon {
        color: #6c7293;
      }
    }

    .actionCell {
      display: flex;
      gap: 12px;
      color: #cecfd2;

      .actionButton {
        font-size: 16px;
        cursor: pointer;
      }

      .deleteButton {
        &:hover {
          color: #ff4d4f;
        }
      }
    }
  }
}
