.messageCentrePage {
  .pageTitle {
    font-weight: 700;
    font-size: 28px;
    line-height: 33px;
    color: #ececed;
    margin-bottom: 24px;
  }
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    .publishButton {
      width: 211px;
      height: 44px;
    }
    @media (max-width: 1200px) {
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      :global {
        .ant-btn {
          width: 100%;
        }
      }
    }
  }
  .filterButtons {
    display: flex;
    gap: 8px;

    .filterButton {
      border: 1px solid #333741;
      border-radius: 8px;
      color: #f5f5f6;

      &:hover {
        background: none;
        color: #f5f5f6;
      }
    }
    :global {
      .ant-btn-primary {
        background: #002266;
        box-shadow: inset 0px 0px 0px 1px rgba(12, 17, 29, 0.18),
          inset 0px -2px 0px rgba(12, 17, 29, 0.05);
        border-radius: 8px;
      }
    }
    @media (max-width: 1200px) {
      width: 100%;
      overflow: auto;
      margin: 0;
      padding-bottom: 16px;
    }
  }

  .contentContainer {
    display: grid;
    grid-template-columns: 352px 1fr;
    gap: 20px;
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .messageList {
    height: 655px;
    border-radius: 6px;
    overflow-x: hidden;
    overflow-y: auto;
    border: 1px solid #333741;
    padding: 20px 12px;

    .dateGroup {
      margin-top: 24px;
      .dateHeader {
        font-weight: 500;
        font-size: 18px;
        line-height: 21px;
        color: #f5f5f6;
        margin-bottom: 12px;
      }
    }

    .messageItem {
      display: block;
      cursor: pointer;
      padding: 12px 8px;
      border-radius: 4px;
      border-bottom: none;

      &:hover {
        background: #1f242f;
      }

      &.activeMessage {
        background: #1f242f;
      }

      .messageTime {
        font-weight: 400;
        font-size: 12px;
        line-height: 14px;
        color: #f5f5f6;
        margin-bottom: 6px;
      }

      .messageContent {
        display: flex;
        align-items: center;
        gap: 4px;
        .messageContentRight {
          flex: 1;
          overflow: hidden;
        }

        .messageBadge {
          width: 38px;
          height: 38px;
        }

        .messageTitle {
          font-weight: 600;
          font-size: 14px;
          line-height: 16px;
          color: #f5f5f6;
          margin-bottom: 2px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .messageDescription {
          font-weight: 400;
          font-size: 14px;
          line-height: 18px;
          color: #94969c;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
    .divider {
      border: 1px solid #333741;
      margin: 4px 0;
    }
  }

  .messageDetail {
    height: 655px;
    border-radius: 6px;
    padding: 20px;
    border: 1px solid #1f242f;

    .detailHeader {
      margin-bottom: 24px;

      .detailDate {
        font-weight: 400;
        font-size: 12px;
        line-height: 14px;
        color: #f5f5f6;
        margin-bottom: 24px;
      }

      .detailTitle {
        font-weight: 600;
        font-size: 20px;
        line-height: 23px;
        color: #f5f5f6;
      }
    }

    .detailContent {
      font-weight: 400;
      font-size: 18px;
      line-height: 21px;
      color: #cecfd2;
      height: calc(100% - 100px);
      overflow: auto;
    }
  }
}
