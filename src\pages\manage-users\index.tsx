import React, { useEffect, useState } from "react";
import { Typography, Input, Button, Table, message, Modal } from "antd";
import {
  SearchOutlined,
  ExportOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import styles from "./style.module.less";
import AddUserModal from "./AddUserModal";
import type { UserFormValues } from "./AddUserModal";
import {
  createUser,
  deleteUser,
  getUserList,
  updateUser,
} from "../../services/auth";
import { type UserInfo, UserRole, type UserRoleType } from "../../types/User";
import { USER_ROLE_MAP } from "../../constant/login";
import Icon from "../../components/Icon";
import { useNavigate } from "react-router-dom";

const { Title } = Typography;

const ManageUsers: React.FC = () => {
  const navigate = useNavigate();
  const [userData, setData] = useState<UserInfo[]>([]);
  const [searchText, setSearchText] = useState<string>("");
  const [isAddUserModalVisible, setIsAddUserModalVisible] =
    useState<boolean>(false);
  const [selectedUser, setSelectedUser] = useState<UserInfo | null>(null);

  const fetchList = () => {
    getUserList().then((data: UserInfo[]) => {
      setData(data);
    });
  };

  useEffect(() => {
    fetchList();
  }, [searchText]);

  const handleEdit = (id: string) => {
    const user = userData.find((user) => user.id === id);
    if (!user) {
      return;
    }
    setSelectedUser(user);
    setIsAddUserModalVisible(true);
  };

  const handleDelete = (id: string) => {
    const user = userData.find((user) => user.id === id);
    if (!user) return;

    Modal.confirm({
      title: "Delete User",
      content:
        "Are you sure you want to delete this user? This action cannot be undone.",
      okText: "Delete User",
      icon: null, // 移除默认图标
      closable: true,
      closeIcon: <CloseOutlined style={{ color: "#94969C" }} />,
      centered: true,
      cancelText: "Cancel",
      onOk: () => {
        deleteUser(id).then(() => {
          fetchList();
          message.success("User deleted successfully");
        });
      },
    });
  };

  const columns = [
    {
      title: "UID",
      dataIndex: "id",
      key: "id",
      sorter: (a: UserInfo, b: UserInfo) =>
        a.id!.toString().localeCompare(b.id!.toString()),
    },
    {
      title: "Username",
      dataIndex: "username",
      key: "username",
      sorter: (a: UserInfo, b: UserInfo) =>
        a.username!.localeCompare(b.username!),
    },
    {
      title: "Real Name",
      dataIndex: "real_name",
      key: "real_name",
      render: (text: string) => (text !== "0" ? text : ""),
      sorter: (a: UserInfo, b: UserInfo) =>
        a.real_name!.localeCompare(b.real_name!),
    },
    {
      title: "Role",
      dataIndex: "role",
      key: "role",
      sorter: (a: UserInfo, b: UserInfo) => a.role! - b.role!,
      render: (role: UserRoleType) => USER_ROLE_MAP[role],
    },
    {
      title: "Stock Project",
      dataIndex: "stockProject",
      key: "stockProject",
      render: (_: any, record: UserInfo) => (
        <Button
          type="link"
          onClick={() => navigate(`/pledged-projects?uid=${record.id}`)}
        >
          Views
        </Button>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: any, record: UserInfo) => (
        <div className={styles.actionCell}>
          <EditOutlined
            className={styles.actionButton}
            onClick={() => handleEdit(record.id!)}
          />
          <DeleteOutlined
            className={`${styles.actionButton} ${styles.deleteButton}`}
            onClick={() => handleDelete(record.id!)}
          />
        </div>
      ),
    },
  ];

  const filteredData = userData.filter(
    (user) =>
      user.username!.toLowerCase().includes(searchText.toLowerCase()) ||
      user.id!.toString().toLowerCase().includes(searchText.toLowerCase()) ||
      (user.stockProject?.name
        .toLowerCase()
        .includes(searchText.toLowerCase()) ??
        false)
  );

  const handleExport = () => {
    console.log("导出用户数据");
    // 导出功能实现
  };

  const handleAddUser = () => {
    setIsAddUserModalVisible(true);
  };

  const handleAddUserCancel = () => {
    setSelectedUser(null);
    setIsAddUserModalVisible(false);
  };

  const handleAddUserSubmit = (values: UserFormValues) => {
    const data = { ...values, confirmPassword: undefined };
    if (selectedUser) {
      updateUser(selectedUser.id!, data).then(() => {
        message.success(`User ${values.username} updated successfully`);
        setIsAddUserModalVisible(false);
        setSelectedUser(null);
        fetchList();
      });
    } else {
      createUser(data).then(() => {
        message.success(`User ${values.username} added successfully`);
        setIsAddUserModalVisible(false);
        setSelectedUser(null);
        fetchList();
      });
    }
  };

  return (
    <div className={styles.container}>
      <div className={`${styles.header} common-page-header-flex`}>
        <Title level={2}>Manage User</Title>
        <Button
          type="primary"
          style={{ width: 277, height: 44 }}
          icon={<PlusOutlined />}
          onClick={handleAddUser}
        >
          Add User
        </Button>
      </div>
      <div className={styles.actions}>
        <Input
          placeholder="Search"
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          className={styles.searchInput}
        />
        {/* <Button
          icon={<Icon name="export" size={16} color="#CECFD2" />}
          onClick={handleExport}
          className={"export-button"}
        >
          Export
        </Button> */}
      </div>
      <Table
        dataSource={filteredData}
        columns={columns}
        rowKey="username"
        className={styles.table}
        pagination={false}
        size="middle"
      />
      <AddUserModal
        user={selectedUser}
        visible={isAddUserModalVisible}
        onCancel={handleAddUserCancel}
        onOk={handleAddUserSubmit}
      />
    </div>
  );
};

export default ManageUsers;
