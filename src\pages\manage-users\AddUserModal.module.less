.addUserModal {
  :global {
    .ant-modal-content {
      background-color: #0c111d;
      border-radius: 8px;
      overflow: hidden;
      padding: 40px;
    }

    .ant-modal-header {
      background-color: transparent;
      border-bottom: none;
      padding: 0 0 16px 0;
    }

    .ant-modal-title {
      color: #ffffff;
      font-size: 20px;
      font-weight: 500;
    }

    .ant-modal-body {
      padding: 0;
    }

    .ant-form-item-label > label {
      color: #ffffff;
      font-size: 14px;
    }

    .ant-input,
    .ant-input-password {
      color: #ffffff;
      height: 35px;

      &::placeholder {
        font-weight: 400;
        font-size: 16px;
        line-height: 19px;
        color: #85888e;
      }
    }

    .ant-input-password .ant-input {
      height: 24px;
      background-color: transparent;
    }

    .ant-select-selector {
      height: 35px;
    }

    .ant-select-selection-placeholder,
    .ant-select-selection-item {
      line-height: 42px !important;
      color: rgba(255, 255, 255, 0.5);
    }

    .ant-select-selection-item {
      color: #ffffff;
    }

    .ant-select-arrow {
      color: rgba(255, 255, 255, 0.5);
    }

    .ant-form-item {
      margin-bottom: 20px;
    }
  }
}

.modalFooter {
  display: flex;
  justify-content: space-between;
  margin-top: 32px;

  .cancelButton,
  .doneButton {
    flex: 1;
    height: 42px;
    border-radius: 6px;
    font-size: 16px;
  }

  .cancelButton {
    margin-right: 12px;
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.3);
    }
  }

  .doneButton {
    margin-left: 12px;
    background-color: #2563eb;
    border: none;

    &:hover {
      background-color: #1d4ed8;
    }
  }
}
