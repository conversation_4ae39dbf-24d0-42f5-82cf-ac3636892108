.editModal {
  :global {
    .ant-modal-content {
      background-color: #0c111d;
      border-radius: 8px;
      border: 1px solid #2a2f3a;
      padding: 40px;
    }

    .ant-modal-header {
      background-color: transparent;
      border-bottom: none;
      margin-bottom: 24px;
      padding: 0;
    }

    .ant-modal-title {
      font-weight: 600;
      font-size: 20px;
      line-height: 23px;
      color: #f5f5f6;
      line-height: 40px;
    }

    .ant-modal-body {
      padding: 0;
    }
    .ant-form-item {
      margin-bottom: 20px;
    }

    .ant-form-item-label > label {
      color: #f5f5f6;
      font-size: 14px;
      font-weight: 500;
    }

    .ant-input {
      background-color: #1f242f;
      border-color: #333741;
      color: #f5f5f6;
      border-radius: 4px;
      height: 40px;

      &:hover,
      &:focus {
        border-color: #3e4452;
      }
    }
  }

  .closeIcon {
    color: #94969c;
    font-size: 20px;
    font-weight: 400;
    cursor: pointer;
  }

  .submitButtonContainer {
    margin-top: 66px;
    margin-bottom: 0;
  }

  .submitButton {
    width: 100%;
    height: 43px;
    background-color: #155eef;
    border: none;
    border-radius: 8px;
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: #2970ff;
    }
  }
}
