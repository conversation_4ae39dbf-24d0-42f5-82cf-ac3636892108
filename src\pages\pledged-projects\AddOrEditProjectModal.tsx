import React, { useEffect, useRef, useState } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  DatePicker,
  message,
} from "antd";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import styles from "./AddProjectModal.module.less";
import {
  createPledgeProject,
  getTickerRealtimeData,
  updatePledgeProject,
} from "../../services/pledgedProjects";
import type { PledgedProject } from "../../types/pledgedProject";
import dayjs from "dayjs";
import useBorrowersAndLenders from "../../hooks/useBorrowersAndLenders";

interface AddOrEditProjectModalProps {
  projectData?: PledgedProject | null;
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
}

const AddOrEditProjectModal: React.FC<AddOrEditProjectModalProps> = ({
  projectData,
  visible,
  onCancel,
  onOk,
}) => {
  const [form] = Form.useForm();
  const getStockInfoTimer = useRef<any>(null);
  const { borrowers, lenders } = useBorrowersAndLenders();

  useEffect(() => {
    if (visible) {
      form.resetFields();
      if (projectData) {
        form.setFieldsValue({
          stock_name: projectData.stock_name,
          stock_code: projectData.stock_code,
          borrower_id:
            projectData?.borrower?.borrower_id ||
            projectData?.relations?.[0]?.borrower_id,
          stock_price: projectData.stock_price,
          start_date: dayjs(projectData.start_date),
          end_date: dayjs(projectData.end_date),
          warninglineconfig: projectData.warninglineconfig,
          alertlineconfig: projectData.alertlineconfig,
          liquidationlineconfig: projectData.liquidationlineconfig,
          warning_line: projectData.warning_line,
          alert_line: projectData.alert_line,
          liquidation_line: projectData.liquidation_line,
          pledge_rate: projectData.pledge_rate,
          required_shares: projectData.required_shares,
          pledge_price:
            projectData.pledge_price ??
            (
              Number(projectData.stock_price) *
              (Number(projectData.pledge_rate) / 100)
            ).toFixed(2),
          lenders:
            (projectData?.lenders || projectData?.relations)?.map(
              (item: any) => ({
                lender_id: item.lender_id,
                amount: item.borrowed_amount,
              })
            ) || [],
        });
      } else {
        form.setFieldsValue({
          lenders: [{}],
        });
      }
    }
  }, [visible, projectData]);

  const handleSave = (values: any) => {
    const data = { ...values };
    data.start_date = dayjs(values.start_date).format("YYYY-MM-DD");
    data.end_date = dayjs(values.end_date).format("YYYY-MM-DD");
    if (projectData) {
      updatePledgeProject({
        project_id: projectData.project_id,
        ...data,
      }).then(() => {
        message.success("Project updated successfully");
        onOk(data);
      });
    } else {
      createPledgeProject(data).then(() => {
        message.success("Project added successfully");
        onOk(data);
      });
    }
  };

  const handleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        handleSave(values);
      })
      .catch((info) => {
        console.log("Validation failed:", info);
      });
  };

  const getStockPrice = () => {
    const stock_code = form.getFieldValue("stock_code");
    const start_date = form.getFieldValue("start_date");
    if (!stock_code) {
      return;
    }
    getTickerRealtimeData({
      ticker: stock_code,
      ...(start_date
        ? { start_date: dayjs(start_date).format("YYYY-MM-DD") }
        : {}),
    })
      .then((res) => {
        form.setFieldsValue({
          stock_price: res[0].high,
        });
      })
      .finally(() => {});
  };

  const handleStartDateChange = (date: any) => {
    getStockPrice();
  };
  const handleStockCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    clearTimeout(getStockInfoTimer.current);
    getStockInfoTimer.current = setTimeout(() => {
      const value = e.target.value;
      if (/^\d+$/.test(value)) {
        getStockPrice();
      }
    }, 500);
  };

  const handleFormValuesChange = (changedValues: any, allValues: any) => {
    let warninglineconfig = parseFloat(allValues.warninglineconfig);
    let alertlineconfig = parseFloat(allValues.alertlineconfig);
    let liquidationlineconfig = parseFloat(allValues.liquidationlineconfig);

    let initialPrice = parseFloat(allValues.stock_price);
    const isChangePledgeRatio =
      Object.keys(changedValues).includes("pledge_rate");
    const isChangeBorrowed =
      Object.keys(changedValues).includes("lenders") &&
      changedValues.lenders.length > 0 &&
      changedValues.lenders.some((lender: any) =>
        Object.keys(lender || {}).includes("amount")
      );
    const isChangeRequiredShares =
      Object.keys(changedValues).includes("required_shares");
    const pledgeRatio = isChangePledgeRatio
      ? parseFloat(changedValues.pledge_rate) / 100
      : parseFloat(allValues.pledge_rate) / 100;
    const totalBorrowed = (allValues.lenders || []).reduce(
      (total: number, lender: any) =>
        total + (lender?.amount ? parseFloat(lender?.amount) : 0),
      0
    );

    let requiredShares = isChangeRequiredShares
      ? changedValues.required_shares
      : allValues.required_shares;

    if (isChangeBorrowed || isChangeRequiredShares) {
      if (isChangeBorrowed) {
        // 如果修改借款金额，根据股票数量和质押率，推导出requiredShares
        requiredShares = totalBorrowed / (initialPrice * pledgeRatio);
        if (!isNaN(requiredShares)) {
          requiredShares = Number(requiredShares.toFixed(2));
          form.setFieldsValue({
            required_shares: requiredShares,
          });
        }
      }
      if (isChangeRequiredShares) {
        // 如果修改股票数量，根据借款金额 和质押率，推导出initialPrice
        initialPrice = totalBorrowed / (requiredShares * pledgeRatio);
        if (!isNaN(initialPrice)) {
          initialPrice = Number(initialPrice.toFixed(2));
          form.setFieldsValue({
            stock_price: initialPrice,
          });
        }
      }
    } else {
      requiredShares = totalBorrowed / (initialPrice * pledgeRatio);
      if (!isNaN(requiredShares)) {
        requiredShares = Number(requiredShares.toFixed(2));
        form.setFieldsValue({
          required_shares: requiredShares,
        });
      }
    }
    if (pledgeRatio > 0.8) {
      warninglineconfig = 1.2;
      alertlineconfig = 1.15;
      liquidationlineconfig = 1.1;
    } else if (pledgeRatio > 0.7 && pledgeRatio <= 0.8) {
      warninglineconfig = 1.25;
      alertlineconfig = 1.17;
      liquidationlineconfig = 1.1;
    } else {
      warninglineconfig = 1.4;
      alertlineconfig = 1.2;
      liquidationlineconfig = 1.15;
    }

    if (Object.keys(changedValues).includes("warninglineconfig")) {
      warninglineconfig = parseFloat(allValues.warninglineconfig);
      if (warninglineconfig < 1) {
        form.setFieldsValue({
          warninglineconfig: 1,
        });
      }
    } else {
      form.setFieldsValue({
        warninglineconfig: warninglineconfig,
      });
    }
    if (Object.keys(changedValues).includes("alertlineconfig")) {
      alertlineconfig = parseFloat(allValues.alertlineconfig);
      if (alertlineconfig < 1) {
        form.setFieldsValue({
          alertlineconfig: 1,
        });
      }
    } else {
      form.setFieldsValue({
        alertlineconfig: alertlineconfig,
      });
    }
    if (Object.keys(changedValues).includes("liquidationlineconfig")) {
      liquidationlineconfig = parseFloat(allValues.liquidationlineconfig);
      if (liquidationlineconfig < 1) {
        form.setFieldsValue({
          liquidationlineconfig: 1,
        });
      }
    } else {
      form.setFieldsValue({
        liquidationlineconfig: liquidationlineconfig,
      });
    }

    let warningLine = initialPrice * pledgeRatio * warninglineconfig;
    if (!isNaN(warningLine)) {
      warningLine = Number(warningLine.toFixed(2));
      form.setFieldsValue({
        warning_line: warningLine,
      });
    }
    let alertLine = initialPrice * pledgeRatio * alertlineconfig;
    if (!isNaN(alertLine)) {
      alertLine = Number(alertLine.toFixed(2));
      form.setFieldsValue({
        alert_line: alertLine,
      });
    }

    let liquidationLine = initialPrice * pledgeRatio * liquidationlineconfig;
    if (!isNaN(liquidationLine)) {
      liquidationLine = Number(liquidationLine.toFixed(2));
      form.setFieldsValue({
        liquidation_line: liquidationLine,
      });
    }
    let pledgePrice = initialPrice * pledgeRatio;
    if (!isNaN(pledgePrice)) {
      pledgePrice = Number(pledgePrice.toFixed(2));
      form.setFieldsValue({
        pledge_price: pledgePrice,
      });
    }
  };

  return (
    <Modal
      title={projectData ? "Edit Project" : "Add New Project"}
      open={visible}
      footer={null}
      onCancel={onCancel}
      width={840}
      className={styles.addProjectModal}
      centered
      maskClosable={false}
    >
      <Form
        form={form}
        layout="vertical"
        className={styles.form}
        onValuesChange={handleFormValuesChange}
        initialValues={{
          duration: { hours: "00", min: "00", sec: "00" },
        }}
      >
        <Row gutter={16}>
          <Col span={12} xs={24} sm={24} md={12}>
            <Form.Item
              name="stock_name"
              label="Stock Name"
              rules={[{ required: true, message: "Please enter stock name" }]}
            >
              <Input placeholder="eg. IBM" />
            </Form.Item>
          </Col>
          <Col span={12} xs={24} sm={24} md={12}>
            <Form.Item
              name="stock_code"
              label="Stock Code"
              rules={[
                { required: true, message: "Please enter stock code" },
                {
                  validator(_, value) {
                    if (value && !/^\d+$/.test(value)) {
                      return Promise.reject(
                        new Error("Stock code must be a number")
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input placeholder="eg. 0186" onChange={handleStockCodeChange} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12} xs={24} sm={24} md={12}>
            <Form.Item
              name="borrower_id"
              label="Borrower"
              rules={[{ required: true, message: "Please select borrower" }]}
            >
              <Select
                placeholder="Select borrower from list"
                options={borrowers}
                disabled={!!projectData}
              />
            </Form.Item>
          </Col>
          <Col span={12} xs={24} sm={24} md={12}>
            <Form.Item
              name="stock_price"
              label="Stock Price"
              rules={[{ required: true, message: "Please enter stock price" }]}
            >
              <Input placeholder="eg. Price" />
            </Form.Item>
          </Col>
        </Row>

        <div className={styles.lenderTitle}>Lender Detail</div>
        <Form.List name="lenders">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }, index) => (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      {...restField}
                      label="Lender"
                      name={[name, "lender_id"]}
                      rules={[
                        { required: true, message: "Please select lender" },
                        () => ({
                          validator(_, value) {
                            if (value) {
                              const lenders = form.getFieldValue("lenders");
                              if (
                                lenders.length > 0 &&
                                lenders.some(
                                  (lender: any, index: number) =>
                                    lender.lender_id === value && index !== name
                                )
                              ) {
                                return Promise.reject(
                                  new Error("Lender already exists")
                                );
                              }
                              return Promise.resolve();
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                      validateTrigger={["onChange", "onBlur"]}
                    >
                      <Select
                        placeholder="Select lender from list"
                        options={lenders}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12} className={styles.lenderAmount}>
                    <Form.Item
                      {...restField}
                      label="Borrowed Amount"
                      name={[name, "amount"]}
                      rules={[
                        {
                          required: true,
                          message: "Please enter Borrowed Amount",
                        },
                        {
                          validator(_, value) {
                            if (value) {
                              if (isNaN(Number(value))) {
                                return Promise.reject(
                                  new Error("Borrowed Amount must be a number")
                                );
                              }
                              if (parseFloat(value) < 1) {
                                return Promise.reject(
                                  new Error(
                                    "Borrowed Amount must be greater than 1"
                                  )
                                );
                              }
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                    >
                      <Input
                        placeholder="eg. 10,000"
                        className={styles.amountInput}
                      />
                    </Form.Item>
                    {index > 0 && (
                      <span className={styles.removeButton}>
                        <MinusCircleOutlined onClick={() => remove(name)} />
                      </span>
                    )}
                  </Col>
                </Row>
              ))}
              <div className={styles.addUserButton}>
                <Button
                  className={styles.addButton}
                  type="primary"
                  onClick={() => add()}
                  icon={<PlusOutlined />}
                >
                  Add Lender
                </Button>
              </div>
            </>
          )}
        </Form.List>

        <Row gutter={16}>
          <Col span={12} xs={24} sm={24} md={12}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="pledge_rate"
                  label="Pledge Rate %"
                  rules={[
                    { required: true, message: "Please enter pledge rate" },
                  ]}
                >
                  <Input placeholder="eg. 70" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="pledge_price" label="Pledge Price">
                  <Input readOnly placeholder="eg. Price" />
                </Form.Item>
              </Col>
            </Row>
          </Col>
          <Col span={12} xs={24} sm={24} md={12}>
            <Form.Item
              name="required_shares"
              label="Required Shares"
              rules={[
                { required: true, message: "Please enter required shares" },
              ]}
            >
              <Input placeholder="eg. 10,000" />
            </Form.Item>
          </Col>
          {/* <Col span={8} xs={24} sm={24} md={8}>
            <Form.Item
              name="pledgeprice"
              label="Pledge Price"
              rules={[{ required: true, message: "Please enter pledge price" }]}
            >
              <Input placeholder="eg. 8.20" />
            </Form.Item>
          </Col> */}
        </Row>

        <Row gutter={16}>
          <Col span={12} xs={24} sm={24} md={12}>
            <Form.Item
              name="start_date"
              label="Start Date"
              rules={[{ required: true, message: "Please enter start date" }]}
            >
              <DatePicker
                style={{ width: "100%" }}
                format="YYYY-MM-DD"
                onChange={handleStartDateChange}
              />
            </Form.Item>
          </Col>
          <Col span={12} xs={24} sm={24} md={12}>
            <Form.Item
              name="end_date"
              label="End Date"
              rules={[{ required: true, message: "Please enter end date" }]}
            >
              <DatePicker style={{ width: "100%" }} format="YYYY-MM-DD" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8} xs={24} sm={24} md={8}>
            <Form.Item
              name="warninglineconfig"
              label="Warning Line Settings"
              initialValue={1.4}
              rules={[
                { required: true, message: "Please enter warning line config" },
              ]}
            >
              <Input placeholder="eg. 1.4" />
            </Form.Item>
          </Col>
          <Col span={8} xs={24} sm={24} md={8}>
            <Form.Item
              name="alertlineconfig"
              label="Alert Line Settings"
              initialValue={1.2}
              rules={[
                { required: true, message: "Please enter alert line config" },
              ]}
            >
              <Input placeholder="eg. 1.2" />
            </Form.Item>
          </Col>
          <Col span={8} xs={24} sm={24} md={8}>
            <Form.Item
              name="liquidationlineconfig"
              label="Liquidation Line Settings"
              initialValue={1.1}
              rules={[
                {
                  required: true,
                  message: "Please enter liquidation line config",
                },
              ]}
            >
              <Input placeholder="eg. 1.1" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8} xs={24} sm={24} md={8}>
            <Form.Item
              name="warning_line"
              label="Warning Line"
              rules={[{ required: true, message: "Please enter warning line" }]}
            >
              <Input placeholder="eg. 1.4" />
            </Form.Item>
          </Col>
          <Col span={8} xs={24} sm={24} md={8}>
            <Form.Item
              name="alert_line"
              label="Alert Line"
              rules={[{ required: true, message: "Please enter alert line" }]}
            >
              <Input placeholder="eg. 1.2" />
            </Form.Item>
          </Col>
          <Col span={8} xs={24} sm={24} md={8}>
            <Form.Item
              name="liquidation_line"
              label="Liquidation Line"
              rules={[
                { required: true, message: "Please enter liquidation line" },
              ]}
            >
              <Input placeholder="eg. 1.1" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <div className={styles.requiredShares}>
              <div>Required Shares</div>
              <div>Borrowed Amount / (Initial Price × Pledge Ratio)</div>
              <div>
                <Form.Item noStyle shouldUpdate>
                  {() => {
                    return form.getFieldValue("required_shares");
                  }}
                </Form.Item>
              </div>
            </div>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <div className={styles.requiredShares}>
              <div>Warning Line</div>
              <div>Initial Price * Pledge Ratio * Warning Line Config</div>
              <div>
                <Form.Item noStyle shouldUpdate>
                  {() => {
                    return form.getFieldValue("warning_line");
                  }}
                </Form.Item>
              </div>
            </div>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <div className={styles.requiredShares}>
              <div>Alert Line</div>
              <div>Initial Price * Pledge Ratio * Alert Line Config</div>
              <div>
                <Form.Item noStyle shouldUpdate>
                  {() => {
                    return form.getFieldValue("alert_line");
                  }}
                </Form.Item>
              </div>
            </div>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <div className={styles.requiredShares}>
              <div>Liquidation Line</div>
              <div>Initial Price * Pledge Ratio * Liquidation Line Config</div>
              <div>
                <Form.Item noStyle shouldUpdate>
                  {() => {
                    return form.getFieldValue("liquidation_line");
                  }}
                </Form.Item>
              </div>
            </div>
          </Col>
        </Row>

        <div className={styles.formActions}>
          <Button className={styles.cancelButton} onClick={onCancel}>
            Cancel
          </Button>
          <Button
            type="primary"
            className={styles.addButton}
            onClick={handleSubmit}
          >
            {projectData ? "Update" : "Add"} Project
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default AddOrEditProjectModal;
