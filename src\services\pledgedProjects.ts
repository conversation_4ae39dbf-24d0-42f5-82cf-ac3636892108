import type {
  NowStockInfo,
  PledgedProject,
  PledgedProjectLender,
  RiskOverviewData,
  TickerRealtimeData,
} from "../types/pledgedProject";
import { post } from "../utils/request";

export const getHomeDashboardData = async (data: any) => {
  return post<{ project_list: PledgedProject[]; statistics: RiskOverviewData }>(
    "/Project/getPledgeProjectList",
    data
  );
};

export const getProjectById = async (data: any) => {
  return post<{
    project: {
      borrower: { borrower_id: number; borrower_name: string };
      lenders: PledgedProjectLender[];
      project: PledgedProject;
    };
  }>("/Project/getProjectById", data);
};

export const getPledgeProjectList = async (data: any) => {
  return post<{ project_list: PledgedProject[] }>(
    "/Project/getPledgeProjectList",
    data
  );
};

export const createPledgeProject = async (data: any) => {
  return post("/Project/createPledgeProject", data);
};

export const updatePledgeProject = async (data: any) => {
  return post("/Project/editPledgeProject", data);
};

export const deletePledgeProject = async (data: any) => {
  return post("/Project/deletePledgeProject", data);
};

export const getTickerRealtimeData = async (data: any) => {
  return post<TickerRealtimeData[]>("/Ticker/get_ticker_realtime_data", data);
};
export const getTickerRealtimeList = async (data: any) => {
  const list = await post<
    TickerRealtimeData[] | { data: TickerRealtimeData[] }
  >("/Ticker/get_ticker_realtime_list", data);
  return Array.isArray(list) ? list : list.data;
};
export const getNowStockInfo = async (data: any) => {
  return post<NowStockInfo[]>("/Project/getNowStockInfo", data);
};

export const getProjectNews = async (data: any) => {
  return post<any>("/News/getNewsList", data);
};

export const getSHInfo = async (data: any) => {
  return post<any>("/pdf/getLatestResultByProjectId", data);
};
