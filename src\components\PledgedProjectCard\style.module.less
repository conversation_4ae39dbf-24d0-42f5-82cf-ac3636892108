.pledgedProjectCard {
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
  transition: all 0.3s;
  cursor: pointer;

  :global {
    .ant-card-body {
      padding: 20px;
    }
  }
  //  box-shadow闪烁动画
  @keyframes criticalRiskFlash {
    0% {
      box-shadow: inset 4px 4px 100px transparent;
    }
    50% {
      box-shadow: inset 4px 4px 100px rgba(240, 68, 56, 0.41);
    }
    100% {
      box-shadow: inset 4px 4px 100px transparent;
    }
  }
  &.criticalRisk {
    border: 1px solid #f04438;
    animation: criticalRiskFlash 2s ease-in-out infinite;
  }

  &.highRisk {
    border: 1px solid #fa541c;
  }

  &.mediumRisk {
    border: 1px solid #fdb022;
  }

  &.lowRisk {
    border: 1px solid #333741;
  }
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}
.projectInfoContainer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .projectInfo {
    display: flex;
    flex-direction: column;
  }
}

.projectId {
  font-weight: 500;
  font-size: 20px;
  line-height: 23px;
  color: #f5f5f6;
}

.projectName {
  font-weight: 400;
  font-size: 18px;
  line-height: 21px;
  color: #94969c;
}

.riskTag {
  border-radius: 6px;
  font-weight: 600;
  font-size: 12px;
  line-height: 14px;
  color: #ffffff;
  padding: 4px 8px;
}

.priceSection {
  margin-bottom: 16px;
}

.priceLabel {
  font-weight: 400;
  font-size: 12px;
  line-height: 14px;
  color: #94969c;
  margin-bottom: 4px;
}

.priceValue {
  font-weight: 600;
  font-size: 20px;
  line-height: 23px;
  color: #f5f5f6;
  display: flex;
  align-items: center;
}

.priceChange {
  padding-left: 8px;
  font-weight: 400;
  font-size: 12px;
  line-height: 14px;
  color: #f04438;
}

.priceUp {
  color: #52c41a;
}

.priceDown {
  color: #f5222d;
}

.ratioSection {
}

.ratioLabel {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-weight: 400;
  font-size: 12px;
  line-height: 14px;
  color: #94969c;
}

.ratioValue {
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  color: #f5f5f6;
}

.thresholdSection {
  margin-top: 40px;
  background-color: rgba(0, 0, 0, 0.02);
  padding: 8px 0px;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  .thresholdContainer {
    display: flex;
    .thresholdItem {
      min-width: 58px;
    }
  }
}

.thresholdLabel {
  font-weight: 400;
  font-size: 12px;
  line-height: 14px;
  color: #94969c;

  margin-bottom: 4px;
}

.thresholdValue {
  font-weight: 400;
  font-size: 14px;
  line-height: 16px;

  color: #f5f5f6;
}

.timeRemaining {
  font-weight: 400;
  font-size: 14px;
  line-height: 16px;

  color: #f5f5f6;
  display: flex;
  align-items: center;
}

.clockIcon {
  margin-right: 4px;
}
