import React, { useCallback, useEffect } from "react";
import { Card } from "antd";
import * as echarts from "echarts/core";
import { PieChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
} from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";
import styles from "./style.module.less";
import { PledgedProjectColors } from "../../constant/common";
// 不需要使用翻译

// 注册必需的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  Pie<PERSON>hart,
  CanvasRenderer,
]);

interface RiskOverviewProps {
  totalProjects: number;
  totalLoanAmount: number;
  riskDistribution: {
    critical: { count: number; percentage: number };
    low: { count: number; percentage: number };
    medium: { count: number; percentage: number };
    high: { count: number; percentage: number };
  };
}

const RiskOverview: React.FC<RiskOverviewProps> = ({
  totalProjects,
  totalLoanAmount,
  riskDistribution,
}) => {
  const chartRef = React.useRef<HTMLDivElement>(null);

  const renderChart = useCallback(() => {
    if (!chartRef.current) return;

    // 初始化图表
    const chart = echarts.init(chartRef.current);

    // 准备数据
    const data = [
      {
        name: "Critical",
        value: riskDistribution.critical.count,
        percentage: riskDistribution.critical.percentage,
      },
      {
        name: "High",
        value: riskDistribution.high.count,
        percentage: riskDistribution.high.percentage,
      },
      {
        name: "Medium",
        value: riskDistribution.medium.count,
        percentage: riskDistribution.medium.percentage,
      },
      {
        name: "Low",
        value: riskDistribution.low.count,
        percentage: riskDistribution.low.percentage,
      },
    ];

    // 配置选项
    const option = {
      tooltip: {
        show: false,
        trigger: "item",
        formatter: "{b}: {c}%",
        backgroundColor: "#0c111d",
        textStyle: {
          color: "#ffffff",
        },
      },
      legend: {
        bottom: 0,
        left: 0,
        itemWidth: 10,
        itemHeight: 10,
        icon: "circle",
        textStyle: {
          color: "#ffffff",
          fontSize: 12,
        },
        formatter: function (name: string) {
          // 根据名称找到对应的数据项
          const item = data.find((item) => item.name === name);
          return item ? `${name} (${item.percentage}%)` : name;
        },
      },
      series: [
        {
          name: "Risk Distribution",
          type: "pie",
          radius: ["72%", "90%"], // 内外半径比例，实现环形图
          center: ["45%", "45%"],
          avoidLabelOverlap: false,
          hoverOffset: 0,
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          data: data,
          color: [
            PledgedProjectColors.Critical,
            PledgedProjectColors.High,
            PledgedProjectColors.Medium,
            PledgedProjectColors.Low,
          ], // Critical, High ,Medium,Low
          // width: 157,
          height: 157,
        },
      ],
    };

    // 设置选项并渲染图表
    chart.setOption(option);

    // 响应容器大小变化
    const resizeHandler = () => {
      chart.resize();
    };
    window.addEventListener("resize", resizeHandler);

    // 清理函数
    return () => {
      window.removeEventListener("resize", resizeHandler);
      chart.dispose();
    };
  }, [riskDistribution]); // 添加 riskDistribution 作为依赖项

  useEffect(() => {
    // 调用renderChart并获取清理函数
    const cleanup = renderChart();

    // 返回清理函数以在组件卸载或依赖项变化时执行
    return cleanup;
  }, [renderChart]);

  return (
    <Card
      title="Risk Overview"
      variant="outlined"
      className={`${styles.dashboardCard} ${styles.riskOverviewCard}`}
    >
      <div className={styles.riskOverviewCardContent}>
        <div className={styles.progressContainer}>
          <div className={styles.chartContainer} ref={chartRef} />
        </div>
        <div className={styles.descriptionContainer}>
          <div className={styles.descriptionItem}>
            <p className={styles.descriptionItemLabel}>
              Total Pledged Projects
            </p>
            <p className={styles.descriptionItemValue}>{totalProjects}</p>
          </div>
          <div className={styles.descriptionItem}>
            <p className={styles.descriptionItemLabel}>Total Loan Amount</p>
            <p className={styles.descriptionItemValue}>RM {totalLoanAmount}</p>
          </div>
          <div
            className={`${styles.descriptionItem} ${styles.descriptionItemFlex}`}
          >
            <div>
              <p className={styles.descriptionItemLabel}>Critical Cases</p>
              <p className={styles.descriptionItemValue}>
                {riskDistribution.critical.count}
              </p>
            </div>
            <div className={styles.descriptionItemRiskCase}>
              <p className={styles.descriptionItemLabel}>High Cases</p>
              <p className={styles.descriptionItemValue}>
                {riskDistribution.high.count}
              </p>
            </div>
            <div className={styles.descriptionItemRiskCase}>
              <p className={styles.descriptionItemLabel}>Medium Cases</p>
              <p className={styles.descriptionItemValue}>
                {riskDistribution.medium.count}
              </p>
            </div>
            <div className={styles.descriptionItemRiskCase}>
              <p className={styles.descriptionItemLabel}>Low Cases</p>
              <p className={styles.descriptionItemValue}>
                {riskDistribution.low.count}
              </p>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default RiskOverview;
