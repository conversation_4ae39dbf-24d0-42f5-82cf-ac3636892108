import React, { useEffect } from "react";
import { Modal, Form, Input, Button } from "antd";
import styles from "./EditProjectModal.module.less";
import type { PledgedProject } from "../../types/pledgedProject";

interface EditProjectModalProps {
  visible: boolean;
  onCancel: () => void;
  onSave: (values: any) => void;
  projectData: PledgedProject | null;
}

interface ProjectFormValues {
  sharedPledged: number;
  warningLine: number;
  alertLine: number;
  liquidationLine: number;
}

const EditProjectModal: React.FC<EditProjectModalProps> = ({
  visible,
  onCancel,
  onSave,
  projectData,
}) => {
  const [form] = Form.useForm<ProjectFormValues>();

  useEffect(() => {
    if (visible && projectData) {
      form.setFieldsValue({
        sharedPledged: parseFloat(projectData.stock_price),
        warningLine: parseFloat(projectData.warning_line),
        alertLine: parseFloat(projectData.alert_line),
        liquidationLine: parseFloat(projectData.liquidation_line),
      });
    }
  }, [visible, projectData, form]);

  const handleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        onSave({ ...values, project_id: projectData?.project_id });
      })
      .catch((info) => {
        console.log("Validation failed:", info);
      });
  };

  return (
    <Modal
      title={`${projectData?.stock_name} - ${projectData?.stock_code}`}
      open={visible}
      footer={null}
      onCancel={onCancel}
      width={500}
      className={styles.editProjectModal}
      centered
      maskClosable={false}
    >
      <Form form={form} layout="vertical" className={styles.form}>
        <Form.Item
          name="sharedPledged"
          label="Shared Pledged"
          rules={[{ required: true, message: "Please enter shared pledged" }]}
        >
          <Input placeholder="eg. 10,000" />
        </Form.Item>

        <Form.Item
          name="warningLine"
          label="Warning Line"
          rules={[{ required: true, message: "Please enter warning line" }]}
        >
          <Input placeholder="eg. 8.05" />
        </Form.Item>

        <Form.Item
          name="alertLine"
          label="Alert Line"
          rules={[{ required: true, message: "Please enter alert line" }]}
        >
          <Input placeholder="eg. 7.70" />
        </Form.Item>

        <Form.Item
          name="liquidationLine"
          label="Liquidation Line"
          rules={[{ required: true, message: "Please enter liquidation line" }]}
        >
          <Input placeholder="eg. 7.49" />
        </Form.Item>

        <div className={styles.formActions}>
          <Button
            type="primary"
            className={styles.saveButton}
            onClick={handleSubmit}
            block
          >
            Save & Update
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default EditProjectModal;
