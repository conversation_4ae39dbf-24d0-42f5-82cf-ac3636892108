.reportPage {
  .pageTitle {
    font-weight: 700;
    font-size: 28px;
    line-height: 33px;
    color: #ececed;
    margin-bottom: 24px;
  }

  .tabsContainer {
    display: flex;
    gap: 8px;
    margin-bottom: 24px;

    .tabButton {
      border-radius: 6px;
      color: #ececed;
      padding: 12px 8px;

      &:hover {
        color: #ececed;
      }
    }
    :global {
      .ant-btn-primary {
        background: #2970ff;
      }
    }
  }

  .filterContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .filterItem {
      display: flex;
      align-items: center;
      border: 1px solid #333741 !important;
      border-radius: 4px !important;

      .filterLabel {
        font-weight: 400;
        font-size: 16px;
        line-height: 19px;
        color: #f5f5f6;
        padding-right: 8px;
      }

      .stockSelect {
        width: 265px;
      }
    }
  }

  .tableContainer {
    .reportTable {
      :global {
        .ant-table-tbody {
          .ant-table-cell {
            padding: 18px 24px !important;
          }
        }
      }
      .columnTitle {
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
        color: #f5f5f6;
      }
      .columnValue {
        margin-top: 4px;
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
        color: #94969c;
      }
    }

    .tableRow {
      transition: background-color 0.3s;
    }
  }

  .riskTag {
    border-radius: 6px;
    font-weight: 600;
    font-size: 12px;
    color: #ffffff;
    text-align: center;
    line-height: 14px;
    display: inline-block;
    padding: 4px 8px;
  }
}

.stockName {
  font-weight: 500;
  font-size: 14px;
  line-height: 16px;
  color: #f5f5f6;
}
.stockCode {
  padding-left: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 16px;
  color: #94969c;
}
.projectMsg {
  // 超出2行省略
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
