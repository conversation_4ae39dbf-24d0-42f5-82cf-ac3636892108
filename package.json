{"name": "SPRMS", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noEmit && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.8.1", "antd": "^5.24.9", "axios": "^1.9.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "i18next-browser-languagedetector": "^8.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.5.1", "react-redux": "^9.2.0", "react-router-dom": "^7.5.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "less": "^4.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0"}}