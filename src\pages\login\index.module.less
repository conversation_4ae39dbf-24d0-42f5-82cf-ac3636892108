.container {
  min-height: 100vh;
  background: #11141c;
  display: flex;
  align-items: center;
  justify-content: center;
}

.box {
  padding: 40px 32px;
  border-radius: 12px;
  min-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  margin-bottom: 32px;
  width: 100px;
  height: 96px;
}

.form {
  width: 100%;
  padding: 40px 40px 16px 40px;
  border: 1px solid #333741;
  border-radius: 12px;
}

.input {
  height: 40px;
  border: 1px solid #2e3242;
  background: transparent;
  border-radius: 4px;
  color: #fff;
  &::placeholder {
    color: rgba(255, 255, 255, 0.3);
  }
}

.helpText {
  color: #ff4d4f;
  font-size: 12px;
}

.sliderContainer {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  height: 40px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.sliderIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: white;
  color: #999;
  font-weight: bold;
}

.sliderTrack {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.loginBtn {
  height: 40px;
  background: #1677ff;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  &:hover {
    background: #4096ff;
  }
}

:global {
  .ant-form-item-label > label {
    color: white;
  }

  .ant-input-affix-wrapper {
    background: transparent;
    border: 1px solid #2e3242;

    .ant-input {
      background: transparent;
      color: white;
    }
  }
}
