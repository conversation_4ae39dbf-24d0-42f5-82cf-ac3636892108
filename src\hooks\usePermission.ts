import { useSelector } from "react-redux";
import type { RootState } from "../store";
import type { UserRoleType } from "../types/User";

const usePermission = () => {
  const { userInfo } = useSelector((state: RootState) => state.user);

  const hasPermission = (roles: UserRoleType | UserRoleType[]) => {
    if (!userInfo || !userInfo.role) {
      return false;
    }
    const hasPermission = Array.isArray(roles)
      ? roles.includes(userInfo.role)
      : userInfo.role === roles;
    return hasPermission;
  };

  return {
    hasPermission,
  };
};

export default usePermission;
