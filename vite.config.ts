import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import svgr from "vite-plugin-svgr";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    svgr({
      svgrOptions: {
        // svgr选项
        icon: true,
        // 保留svg的fill属性，这样我们可以通过CSS控制颜色
        svgProps: {
          fill: "currentColor",
        },
      },
    }),
  ],
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true, // 启用 JavaScript 支持
        // 如果需要自定义主题，可以在这里添加 modifyVars
        modifyVars: {
          // 自定义主题变量
          // 'primary-color': '#1DA57A',
        },
      },
    },
  },
  server: {
    port: 3001,
    open: true,
    cors: true,
    proxy: {
      "/api": {
        target: "https://pd.dayinbz.cn/index",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
        secure: true,
      },
    },
  },
});
