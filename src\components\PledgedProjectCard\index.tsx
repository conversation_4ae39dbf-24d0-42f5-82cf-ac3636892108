import React from "react";
import { Card, Progress, Tag, Row, Col } from "antd";
import styles from "./style.module.less";
import type { RiskLevel } from "../../types/pledgedProject";
import {
  calculateTimeLeft,
  getRiskLevelColor,
  getRiskLevelText,
} from "../../utils/utils";
import Icon from "../Icon";

interface PledgedProjectCardProps {
  id: number;
  stockCode: string;
  stockName: string;
  currentPrice: number;
  priceChange: string;
  pledgedPrice: number;
  pledgedRatio: number;
  warningPrice: number;
  alertPrice: number;
  liquidationPrice: number;
  riskLevel: RiskLevel;
  start_date: string;
  end_date: string;
  requiredShares: number;
  onClick?: () => void;
}

const PledgedProjectCard: React.FC<PledgedProjectCardProps> = ({
  id,
  stockCode,
  stockName,
  currentPrice,
  priceChange,
  pledgedPrice,
  pledgedRatio,
  warningPrice,
  alertPrice,
  liquidationPrice,
  riskLevel,
  start_date,
  end_date,
  requiredShares,
  onClick,
}) => {
  const cardClass = `${styles.pledgedProjectCard} ${
    styles[`${riskLevel.toLowerCase()}Risk`]
  }`;
  const isPriceUp = parseFloat(priceChange) > 0;

  return (
    <Card className={cardClass} variant="borderless" onClick={onClick}>
      <div className={styles.cardHeader}>
        <div className={styles.projectInfoContainer}>
          <div className={styles.projectInfo}>
            <div className={styles.projectId}>{stockCode}</div>
            <div className={styles.projectName}>{stockName}</div>
          </div>
          <div className={styles.projectInfo}>
            <div className={styles.projectId}>{requiredShares}</div>
            <div className={styles.projectName}>Required Shares</div>
          </div>
        </div>
        <Tag color={getRiskLevelColor(riskLevel)} className={styles.riskTag}>
          {getRiskLevelText(riskLevel)}
        </Tag>
      </div>

      <Row gutter={16} className={styles.priceSection}>
        <Col span={12}>
          <div className={styles.priceLabel}>Current Price</div>
          <div className={styles.priceValue}>
            {currentPrice}
            <span
              className={`${styles.priceChange} ${
                isPriceUp ? styles.priceUp : styles.priceDown
              }`}
            >
              {isPriceUp ? "+" : ""}
              {priceChange}
            </span>
          </div>
        </Col>
        <Col span={12}>
          <div className={styles.priceLabel}>Pledged Price</div>
          <div className={styles.priceValue}>{pledgedPrice}</div>
        </Col>
      </Row>

      <div className={styles.ratioSection}>
        <div className={styles.ratioLabel}>
          Pledged Ratio
          <span className={styles.ratioValue}>{pledgedRatio}%</span>
        </div>
        <Progress
          percent={pledgedRatio}
          showInfo={false}
          strokeColor={getRiskLevelColor(riskLevel)}
          trailColor="#fff"
          size={{ height: 6 }}
        />
      </div>

      <div className={styles.thresholdSection}>
        <div className={styles.thresholdContainer}>
          <div className={styles.thresholdItem}>
            <div className={styles.thresholdLabel}>Warning</div>
            <div className={styles.thresholdValue}>{warningPrice}</div>
          </div>
          <div className={styles.thresholdItem}>
            <div className={styles.thresholdLabel}>Alert</div>
            <div className={styles.thresholdValue}>{alertPrice}</div>
          </div>
          <div className={styles.thresholdItem}>
            <div className={styles.thresholdLabel}>Liquidation</div>
            <div className={styles.thresholdValue}>{liquidationPrice}</div>
          </div>
        </div>
        <div className={styles.timeRemaining}>
          <Icon
            name="clock"
            className={styles.clockIcon}
            size={16}
            color="#fff"
          />

          {calculateTimeLeft(start_date, end_date)}
        </div>
      </div>
    </Card>
  );
};

export default PledgedProjectCard;
