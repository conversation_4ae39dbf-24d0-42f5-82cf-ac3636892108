import React, { useEffect, useState } from "react";
import { Row, Col, Typography } from "antd";
import RiskOverview from "./RiskOverview";
import EventTimeline from "./EventTimeline";
import PledgedProjects from "./PledgedProjects";
import AuthGuard from "../../components/AuthGuard";
import { UserRole } from "../../types/User";
import { getHomeDashboardData } from "../../services/pledgedProjects";
import type {
  PledgedProject,
  RiskOverviewData,
} from "../../types/pledgedProject";
import type { MessageCentreData } from "../../types/MessageCentre";
import { getEventList } from "../../services/messageCentre";

const { Title } = Typography;

const Dashboard: React.FC = () => {
  const [projects, setProjects] = useState<PledgedProject[]>([]);
  const [riskOverviewData, setRiskOverviewData] =
    useState<RiskOverviewData | null>(null);
  const [eventData, setEventData] = useState<MessageCentreData[]>([]);

  const getPledgeProjects = () => {
    getHomeDashboardData({}).then((data) => {
      setProjects(data.project_list);
      setRiskOverviewData(data.statistics);
    });
  };

  const getEventTimeline = () => {
    getEventList({}).then((data) => {
      setEventData(data);
    });
  };

  useEffect(() => {
    getPledgeProjects();
    getEventTimeline();
  }, []);

  // 处理刷新事件
  const handleRefresh = () => {
    getPledgeProjects();
  };

  return (
    <div className="dashboard-container">
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Title level={2}>Dashboard</Title>
        </Col>
        <AuthGuard
          allowedRoles={UserRole.Regulator}
          noPermissionNode={
            <Col xs={24}>
              <EventTimeline events={eventData} onRefresh={handleRefresh} />
            </Col>
          }
        >
          <Col xs={24} lg={12}>
            <RiskOverview
              totalProjects={riskOverviewData?.total_projects || 0}
              totalLoanAmount={riskOverviewData?.total_borrowed || 0}
              riskDistribution={{
                critical: riskOverviewData?.risk_stats.critical || {
                  count: 0,
                  percentage: 0,
                },
                low: riskOverviewData?.risk_stats.low || {
                  count: 0,
                  percentage: 0,
                },
                medium: riskOverviewData?.risk_stats.medium || {
                  count: 0,
                  percentage: 0,
                },
                high: riskOverviewData?.risk_stats.high || {
                  count: 0,
                  percentage: 0,
                },
              }}
            />
          </Col>
          <Col xs={24} lg={12}>
            <EventTimeline events={eventData} onRefresh={handleRefresh} />
          </Col>
        </AuthGuard>
        <Col span={24}>
          <PledgedProjects projects={projects} onRefresh={handleRefresh} />
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
