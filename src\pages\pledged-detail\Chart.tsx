import React, { useEffect, useState } from "react";
import { Card, Select } from "antd";
import styles from "./style.module.less";
import type { PledgedProject } from "../../types/pledgedProject";
import {
  getTickerRealtimeData,
  getTickerRealtimeList,
} from "../../services/pledgedProjects";
import { splitData } from "../../utils/candlestickChart";
import CandlestickChart from "./CandlestickChart";
import dayjs from "dayjs";
import { PledgedProjectTagColors } from "../../constant/common";

const chartPeriodOptions = [
  { value: 1, label: "1 Day" },
  { value: 2, label: "1 Week" },
  { value: 3, label: "1 Month" },
  { value: 4, label: "1 Year" },
];

const Chart: React.FC<{
  detail: PledgedProject;
}> = ({ detail }) => {
  const [chartPeriod, setChartPeriod] = useState<number>(1);
  const [data0, setData0] = useState<{
    categoryData: (string | number)[];
    values: (string | number)[][];
    seriesConfig?: any[];
  }>({
    categoryData: [],
    values: [],
    seriesConfig: [],
  });

  useEffect(() => {
    if (detail) {
      getTickerRealtimeList({
        project_id: detail.project_id,
        is_time: chartPeriod,
      }).then((res) => {
        if (res) {
          const sortedResult = [...res].sort((a, b) =>
            dayjs(a.date).diff(dayjs(b.date))
          );
          const categoryData = sortedResult.map((item) =>
            chartPeriod === 1
              ? dayjs(item.date).format("HH:mm")
              : dayjs(item.date).format("MM-DD-YYYY")
          );
          const values = [
            sortedResult.map((item) => item.close),
            sortedResult.map((item) => item.low),
            [],
          ];
          setData0({
            categoryData,
            values,
            seriesConfig: [
              {
                symbol: "none",
                name: "close",
                markLine: {
                  symbol: "none",
                  data: [
                    {
                      yAxis: detail?.warning_line,
                      name: "Warning Line",
                      lineStyle: {
                        color: PledgedProjectTagColors.critical.backgroundColor,
                      },
                    },
                  ],
                  label: {
                    formatter: "{b}: {c}",
                    position: "insideEndTop",
                  },
                },
              },
              {
                symbol: "none",
                name: "low",
                markLine: {
                  symbol: "none",
                  data: [
                    {
                      yAxis: detail?.alert_line,
                      name: "Alert Line",
                      lineStyle: {
                        color: PledgedProjectTagColors.high.backgroundColor,
                      },
                    },
                  ],
                  label: {
                    formatter: "{b}: {c}",
                    position: "insideEndTop",
                  },
                },
              },
              {
                symbol: "none",
                name: "line",
                markLine: {
                  symbol: "none",
                  data: [
                    {
                      yAxis: detail?.liquidation_line,
                      name: "Liquidation Line",
                      lineStyle: {
                        color: PledgedProjectTagColors.medium.backgroundColor,
                      },
                    },
                  ],
                  label: {
                    formatter: "{b}: {c}",
                    position: "insideEndTop",
                  },
                },
              },
            ],
          });
        } else {
          setData0({ categoryData: [], values: [] });
        }
      });
    }
  }, [detail, chartPeriod]);

  const handleChartPeriodChange = (value: number) => {
    setChartPeriod(value);
  };

  return (
    <Card className={styles.chartCard} variant="outlined">
      <div className={styles.chartHeader}>
        <div className={styles.chartTitle}>Price Trend</div>
        <div className={styles.chartPeriod}>
          <Select
            defaultValue={chartPeriod}
            style={{ width: 100 }}
            onChange={handleChartPeriodChange}
            options={chartPeriodOptions}
          />
        </div>
      </div>
      <CandlestickChart
        name={
          chartPeriodOptions.find((item) => item.value === chartPeriod)
            ?.label || ""
        }
        data={data0}
        className={styles.chart}
        chartType="line"
        xAxisLabelRotate={chartPeriod === 1 ? 0 : 40}
        grid={{ ...(chartPeriod !== 1 ? { left: "5%" } : {}), bottom: "10%" }}
      />
    </Card>
  );
};

export default Chart;
