import React from "react";
import { Card, List } from "antd";
import styles from "./style.module.less";
import { EventEnum, type MessageCentreData } from "../../types/MessageCentre";
import dayjs from "dayjs";
import { getRelativeDate } from "../../utils/utils";
import alertIcon from "../../assets/message-center/alert-icon.png";
import warningIcon from "../../assets/message-center/warning-icon.png";
import infoIcon from "../../assets/message-center/info-icon.png";

interface EventTimelineProps {
  events: MessageCentreData[];
  onRefresh?: () => void;
}

const DotImage = {
  [EventEnum.Warning]: warningIcon,
  [EventEnum.Alert]: alertIcon,
  [EventEnum.Severely]: alertIcon,
  [EventEnum.Normal]: infoIcon,
  [EventEnum.CloseThePosition]: infoIcon,
  [EventEnum.Info]: infoIcon,
};

const EventTimeline: React.FC<EventTimelineProps> = ({ events, onRefresh }) => {
  return (
    <Card
      title="Event Timeline"
      className={`${styles.dashboardCard} ${styles.eventTimelineCard}`}
    >
      <List
        itemLayout="horizontal"
        dataSource={events}
        renderItem={(item) => (
          <List.Item>
            <List.Item.Meta
              avatar={
                <div className={styles.eventTimelineAvatar}>
                  <div className={styles.eventTimelineAvatarDotContainer}>
                    <img src={DotImage[item.event_type]} alt="" />
                  </div>
                  <div className={styles.eventTimelineAvatarText}>
                    <div className={styles.eventTimelineAvatarDate}>
                      {getRelativeDate(item.create_data)}
                    </div>
                    <div className={styles.eventTimelineAvatarTime}>
                      {dayjs(item.create_data).format("HH:mm A")}
                    </div>
                  </div>
                </div>
              }
              title={item.event_title}
              description={item.event_msg}
            />
          </List.Item>
        )}
      />
    </Card>
  );
};

export default EventTimeline;
