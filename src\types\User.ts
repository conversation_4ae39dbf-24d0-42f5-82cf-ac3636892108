export const UserRole = {
  Regulator: 1, // 监管方
  Lender: 2, // 放款方
  Borrower: 3, // 借贷方
} as const;

export type UserRoleType = (typeof UserRole)[keyof typeof UserRole];

export interface UserInfo {
  id?: string;
  username?: string;
  real_name?: string;
  name?: string;
  avatar?: string;
  email?: string;
  phone?: string;
  role?: UserRoleType;
  permissions?: string[];
  token?: string;
  stockProject?: any;
}
