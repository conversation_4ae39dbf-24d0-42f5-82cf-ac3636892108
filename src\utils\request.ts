import axios, {
  type AxiosResponse,
  type AxiosError,
  type InternalAxiosRequestConfig,
} from "axios";
import { message, Spin } from "antd";
import { LOGIN_TOKEN } from "../constant/login";
import React from "react";
import { createRoot } from "react-dom/client";

let requestCount = 0;

const showLoading = () => {
  if (requestCount === 0) {
    const loadingContainer = document.createElement("div");
    loadingContainer.setAttribute("id", "global-loading-container");
    loadingContainer.style.position = "fixed";
    loadingContainer.style.top = "0";
    loadingContainer.style.left = "0";
    loadingContainer.style.width = "100%";
    loadingContainer.style.height = "100%";
    loadingContainer.style.display = "flex";
    loadingContainer.style.justifyContent = "center";
    loadingContainer.style.alignItems = "center";
    loadingContainer.style.backgroundColor = "rgba(0, 0, 0, 0.1)";
    loadingContainer.style.zIndex = "9999";
    document.body.appendChild(loadingContainer);
    const container = document.getElementById("root");
    if (container) {
      const root = createRoot(loadingContainer);
      root.render(React.createElement(Spin, { size: "large" }));
    }
  }
  requestCount++;
};

const hideLoading = () => {
  requestCount--;
  if (requestCount <= 0) {
    requestCount = 0;
    const loadingContainer = document.getElementById(
      "global-loading-container"
    );
    if (loadingContainer) {
      const root = createRoot(loadingContainer);
      root.unmount();
      document.body.removeChild(loadingContainer);
    }
  }
};

const instance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || "/api",
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
  },
});

instance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 请求配置中没有禁用loading，则显示loading
    if (!config.headers?.get("X-No-Loading")) {
      showLoading();
    }

    const token = localStorage.getItem(LOGIN_TOKEN);

    if (token && config.headers) {
      config.headers.set("Authorization", token);
    }

    return config;
  },
  (error: AxiosError) => {
    hideLoading();
    message.error("Request sending failed");
    return Promise.reject(error);
  }
);

instance.interceptors.response.use(
  (response: AxiosResponse) => {
    if (!response.config.headers?.get("X-No-Loading")) {
      hideLoading();
    }

    const { data } = response;
    //  { code: number, data: any, message: string }
    if (data && (data.code === 200 || data.data)) {
      return data;
    }

    message.error(data.message || "Request failed");
    return Promise.reject(new Error(data.message || "Request failed"));
  },
  (error: AxiosError) => {
    if (error.config && !error.config.headers?.get("X-No-Loading")) {
      hideLoading();
    }

    if (error.response) {
      const { status } = error.response;

      switch (status) {
        case 400:
          message.error("Invalid request parameters");
          break;
        case 401:
          message.error("Unauthorized, please login again");
          localStorage.removeItem(LOGIN_TOKEN);
          window.location.href = "/login";
          break;
        case 403:
          message.error("Access denied");
          break;
        case 404:
          message.error("Requested resource not found");
          break;
        case 500:
          message.error("Server error");
          break;
        default:
          message.error(`Request failed: ${status}`);
      }
    } else if (error.request) {
      message.error("Network error, please check your connection");
    } else {
      message.error(`Request error: ${error.message}`);
    }

    return Promise.reject(error);
  }
);

// 创建不显示loading的配置
export const createNoLoadingConfig = (
  config?: InternalAxiosRequestConfig
): InternalAxiosRequestConfig => {
  const newConfig: InternalAxiosRequestConfig = config
    ? { ...config }
    : { headers: axios.AxiosHeaders.from({}) };

  if (!newConfig.headers) {
    newConfig.headers = axios.AxiosHeaders.from({});
  }

  const headers = newConfig.headers;
  headers.set("X-No-Loading", "true");

  return newConfig;
};

export const get = async <T>(
  url: string,
  params?: any,
  config?: InternalAxiosRequestConfig,
  showLoading: boolean = true,
  fullResponse: boolean = false
): Promise<T> => {
  const finalConfig = showLoading ? config : createNoLoadingConfig(config);
  const res: any = await instance.get(url, { params, ...finalConfig });
  return fullResponse ? res : res.data || res;
};

// 处理 FormData 的公共函数
const handleFormData = (data: any): FormData => {
  const formData = new FormData();

  if (data && typeof data === "object" && !(data instanceof FormData)) {
    Object.keys(data).forEach((key) => {
      if (data[key] !== undefined && data[key] !== null) {
        formData.append(key, data[key]);
      }
    });
    return formData;
  }

  return data instanceof FormData ? data : formData;
};

// 创建请求配置的公共函数
const createFormDataConfig = (config?: InternalAxiosRequestConfig) => {
  return {
    ...config,
    headers: {
      ...config?.headers,
      "Content-Type": "multipart/form-data",
    },
  };
};

export const post = async <T>(
  url: string,
  data?: any,
  config?: InternalAxiosRequestConfig,
  isFormData: boolean = false,
  showLoading: boolean = true,
  fullResponse: boolean = false
): Promise<T> => {
  let finalConfig = config;

  if (!showLoading) {
    finalConfig = createNoLoadingConfig(config);
  }

  if (isFormData) {
    const formData = handleFormData(data);
    return instance.post(url, formData, createFormDataConfig(finalConfig));
  }

  const res: any = await instance.post(url, data, finalConfig);
  return fullResponse ? res : res.data || res;
};

export const put = async <T>(
  url: string,
  data?: any,
  config?: InternalAxiosRequestConfig,
  isFormData: boolean = false,
  showLoading: boolean = true,
  fullResponse: boolean = false
): Promise<T> => {
  let finalConfig = config;

  if (!showLoading) {
    finalConfig = createNoLoadingConfig(config);
  }

  if (isFormData) {
    const formData = handleFormData(data);
    return instance.put(url, formData, createFormDataConfig(finalConfig));
  }

  const res: any = await instance.put(url, data, finalConfig);
  return fullResponse ? res : res.data || res;
};

export const del = async <T>(
  url: string,
  config?: InternalAxiosRequestConfig,
  showLoading: boolean = true,
  fullResponse: boolean = false
): Promise<T> => {
  const finalConfig = showLoading ? config : createNoLoadingConfig(config);
  const res: any = await instance.delete(url, finalConfig);
  return fullResponse ? res : res.data || res;
};

export default instance;
