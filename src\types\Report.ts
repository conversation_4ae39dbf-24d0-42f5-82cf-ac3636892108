export interface StockReport {
  id: number;
  project_id: number;
  current_stock_price: string;
  status: string;
  project_msg: string;
  create_time: string;
  update_time: string;
  pledge_price: string;
  stock_name: string;
  stock_code: string;
  stock_price: string;
  start_date: string;
  end_date: string;
  warning_line: string;
  alert_line: string;
  liquidation_line: string;
  old_pledge_price: number;
  required_shares: number;
  total_borrowed: string;
}

export interface StockOption {
  project_id: string;
  stock_name: string;
}
