import React, { useState, useEffect } from "react";
import { Modal, Input, Form } from "antd";
import styles from "./EditModal.module.less";

interface EditModalProps {
  visible: boolean;
  onCancel: () => void;
  onSave: (values: any) => void;
  initialValues: {
    stockName: string;
    stockCode: string;
    sharedPledged: number;
    warningLine: number;
    alertLine: number;
    liquidationLine: number;
  };
}

const EditModal: React.FC<EditModalProps> = ({
  visible,
  onCancel,
  onSave,
  initialValues,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [visible, initialValues, form]);

  const handleSubmit = () => {
    form.validateFields().then((values) => {
      onSave(values);
    });
  };

  const title = `${initialValues?.stockName} - ${initialValues?.stockCode}`;

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      footer={null}
      className={styles.editModal}
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item
          name="sharedPledged"
          label="Shared Pledged"
          rules={[{ required: true, message: "Please enter shared pledged" }]}
        >
          <Input placeholder="Enter shared pledged" />
        </Form.Item>

        <Form.Item
          name="warningLine"
          label="Warning Line"
          rules={[{ required: true, message: "Please enter warning line" }]}
        >
          <Input placeholder="Enter warning line" />
        </Form.Item>

        <Form.Item
          name="alertLine"
          label="Alert Line"
          rules={[{ required: true, message: "Please enter alert line" }]}
        >
          <Input placeholder="Enter alert line" />
        </Form.Item>

        <Form.Item
          name="liquidationLine"
          label="Liquidation Line"
          rules={[{ required: true, message: "Please enter liquidation line" }]}
        >
          <Input placeholder="Enter liquidation line" />
        </Form.Item>

        <Form.Item className={styles.submitButtonContainer}>
          <button type="submit" className={styles.submitButton}>
            Save & Update
          </button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EditModal;
