.addProjectModal {
  @media (max-width: 992px) {
    width: 650px !important;
  }
  :global {
    .ant-modal-content {
      background-color: #161b26;
      border-radius: 8px;
      padding: 40px;
      @media (max-width: 992px) {
        padding: 40px 24px;
      }
    }

    .ant-modal-header {
      background-color: #161b26;
      border-bottom: none;
      margin-bottom: 0;

      .ant-modal-title {
        color: #f5f5f6;
        font-weight: 600;
        font-size: 18px;
      }
    }

    .ant-modal-body {
      padding: 0;
      margin-top: 20px;
    }

    .ant-modal-close {
      color: #6c7293;

      &:hover {
        color: #f5f5f6;
      }
    }

    .ant-form-item-label > label {
      color: #f5f5f6;
      font-size: 14px;
    }

    .ant-select-arrow {
      color: #6c7293;
    }

    .ant-form-item-explain-error {
      color: #ff4d4f;
    }
  }

  .form {
    margin-top: 16px;
    .lenderTitle {
      font-weight: 600;
      font-size: 20px;
      line-height: 23px;
      color: #f5f5f6;
      margin-bottom: 20px;
    }
    .addUserButton {
      display: flex;
      width: 100%;
      justify-content: end;
      @media (max-width: 992px) {
        margin-top: 0;
        margin-bottom: 24px;
      }
      .addButton {
        width: 195px;
        height: 44px;
      }
    }
  }

  .durationItem {
    :global {
      .ant-input-group {
        display: flex;
        gap: 8px;
      }
    }

    .durationInput {
      width: 100%;
      text-align: center;

      :global {
        .ant-input-suffix {
          color: #6c7293;
          margin-left: 4px;
        }
      }
    }
  }

  .formActions {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;

    .cancelButton {
      flex: 1;
      margin-right: 8px;
      background-color: transparent;
      border-color: #2a2f3a;
      color: #f5f5f6;

      &:hover {
        border-color: #6c7293;
        color: #f5f5f6;
      }
    }

    .addButton {
      flex: 1;
      margin-left: 8px;
    }
  }
  .lenderAmount {
    position: relative;
    .amountInput {
      width: calc(100% - 14px);
    }
  }
  .removeButton {
    position: absolute !important;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
  }
  .requiredShares {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    height: 40px;
    padding: 0 24px;
    border-bottom: 1px solid #333741;
    @media (max-width: 992px) {
      margin-top: 8px;
      padding: 0 0 8px;
    }
  }
}
