import { EventEnum } from "../types/MessageCentre";

export const PledgedProjectColors = {
  Critical: "#f04438",
  High: "#fa541c",
  Medium: "#fdb022",
  Low: "#52C41A",
  Default: "#333741",
};

export const PledgedProjectTagColors = {
  critical: { backgroundColor: PledgedProjectColors.Critical, color: "#fff" },
  high: { backgroundColor: PledgedProjectColors.High, color: "#fff" },
  medium: { backgroundColor: PledgedProjectColors.Medium, color: "#000" },
  low: { backgroundColor: PledgedProjectColors.Low, color: "#000" },
};

export const EventMessageEnumMap = {
  [EventEnum.Warning]: "Warning",
  [EventEnum.Alert]: "Alert",
  [EventEnum.CloseThePosition]: "Close The Position",
  [EventEnum.Severely]: "Severely",
  [EventEnum.Normal]: "Normal",
  [EventEnum.Info]: "Info",
};
