<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>全球股票AI分析神器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://fastly.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css"
      rel="stylesheet"
    />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#409EFF",
              secondary: "#67C23A",
              neutral: "#1E293B", // 暗色背景
              dark: "#E2E8F0", // 暗模式下的文本颜色
              stock: "#E6A23C",
              user: "#1A365D", // 暗色模式下的用户消息背景
              userDark: "#2C5282", // 用户消息悬停时的背景色
              darkBg: "#0c111d", // 主背景色
              darkSecondary: "#1E293B", // 次要背景色
              darkBorder: "#334155", // 边框颜色
              darkText: "#E2E8F0", // 主要文本颜色
              darkTextSecondary: "#94A3B8", // 次要文本颜色
            },
            fontFamily: {
              inter: ["Inter", "system-ui", "sans-serif"],
            },
          },
        },
      };
    </script>
    <style type="text/tailwindcss">
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      ::-webkit-scrollbar-track {
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.05);
      }

      ::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.3);
      }
      @layer utilities {
        .content-auto {
          content-visibility: auto;
        }
        .typing-cursor {
          animation: blink 1s step-end infinite;
        }
        @keyframes blink {
          from,
          to {
            opacity: 1;
          }
          50% {
            opacity: 0;
          }
        }
        .preset-btn {
          @apply bg-darkSecondary text-primary px-3 py-1 rounded-full text-sm mr-2 mb-2 inline-block hover:bg-blue-900 transition-colors cursor-pointer;
        }
        .message-enter {
          opacity: 0;
          transform: translateY(10px);
        }
        .message-enter-active {
          opacity: 1;
          transform: translateY(0);
          transition: opacity 300ms, transform 300ms;
        }
        .stock-card {
          @apply bg-darkSecondary rounded-lg shadow-md p-4 mb-4 transform transition-all duration-300 hover:shadow-lg;
        }
        .chart-container {
          @apply h-64 w-full mb-4;
        }
        .user-message {
          @apply bg-user text-darkText p-3 rounded-lg max-w-[90%] ml-auto transition-all duration-200 hover:bg-userDark shadow-sm;
          /* 添加圆角和阴影效果 */
          border-radius: 12px 12px 0 12px;
        }
        .assistant-message {
          @apply bg-neutral text-darkText p-3 rounded-lg max-w-[90%] transition-all duration-200 hover:shadow-md;
          /* 添加圆角和阴影效果 */
          border-radius: 12px 12px 12px 0;
        }
      }
    </style>
  </head>
  <body class="bg-darkBg font-inter min-h-screen flex flex-col text-darkText">
    <div class="container mx-auto px-4 py-8 max-w-4xl flex-1">
      <!-- 标题区域 -->
      <header class="mb-6 text-center">
        <h1 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-darkText">
          全球股票AI分析神器
        </h1>
        <p class="text-darkTextSecondary mt-2">
          输入股票代码并提问，获取AI驱动的专业分析
        </p>
      </header>

      <!-- 对话区域 -->
      <div
        id="chat-container"
        class="bg-darkSecondary rounded-lg shadow-sm p-4 h-[60vh] overflow-y-auto mb-4"
      >
        <div class="space-y-4" id="messages">
          <!-- 系统消息 -->
          <div class="flex items-start mb-4 message-enter">
            <div class="bg-primary text-white rounded-full p-2 mr-3">
              <i class="fa fa-line-chart"></i>
            </div>
            <div class="bg-neutral p-3 rounded-lg max-w-[90%]">
              <p class="text-darkText">
                欢迎使用全球股票AI分析神器！您可以选择预设问题或输入自定义问题，我将为您提供专业的分析和见解。
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="space-y-3">
        <!-- 股票代码输入框 -->
        <div class="relative">
          <div
            class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
          >
            <!-- <i class="fa fa-tag text-gray-400"></i> -->
          </div>
          <!-- 拆分为国家代码下拉框和股票代码输入框 -->
          <div class="relative">
            <input
              type="text"
              id="exchange-identifier"
              class="w-full pl-10 pr-4 py-3 rounded-lg border border-darkBorder bg-darkSecondary text-darkText focus:outline-none focus:ring-2 focus:ring-stock/50"
              placeholder="输入证券所标识 (例如: XKLS)"
              value="XKLS"
            />
          </div>
          <!-- 股票代码输入框 -->
          <div class="relative" style="margin-top: 4px">
            <input
              type="text"
              id="stock-code"
              class="w-full pl-10 pr-4 py-3 rounded-lg border border-darkBorder bg-darkSecondary text-darkText focus:outline-none focus:ring-2 focus:ring-stock/50"
              placeholder="输入股票代码 (例如: 0186)"
            />
          </div>
          <!-- 预设提示词 -->
          <div class="mt-2">
            <p class="text-sm text-darkTextSecondary mb-2">常用问题：</p>
            <div id="preset-questions" class="flex flex-wrap">
              <span
                class="preset-btn"
                data-question="这支股票的股东想要用这只股票进行质押，请根据这支股票的综合表现给我评估一个合理的质押率"
                >质押评估</span
              >
              <span class="preset-btn" data-question="这只股票近期的表现如何？"
                >近期表现</span
              >
              <span
                class="preset-btn"
                data-question="该股票的技术指标显示什么信号？"
                >技术指标</span
              >
              <span class="preset-btn" data-question="这只股票的基本面如何？"
                >基本面分析</span
              >
              <span
                class="preset-btn"
                data-question="该公司最近有哪些重大新闻或公告？"
                >公司新闻</span
              >
              <span
                class="preset-btn"
                data-question="与同行业相比，这只股票的表现如何？"
                >行业对比</span
              >
            </div>
          </div>
        </div>

        <!-- 问题输入框 -->
        <div class="relative">
          <textarea
            id="user-input"
            class="w-full px-4 py-3 rounded-lg border border-darkBorder bg-darkSecondary text-darkText focus:outline-none focus:ring-2 focus:ring-primary/50 resize-none"
            rows="3"
            placeholder="输入您关于该股票的问题..."
          ></textarea>
          <button
            id="send-btn"
            class="absolute right-3 bottom-3 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors flex items-center"
          >
            <i class="fa fa-paper-plane mr-2"></i> 发送
          </button>
        </div>
      </div>

      <!-- 响应模式选择 -->
      <div class="mt-4 flex items-center space-x-4">
        <label class="flex items-center cursor-pointer">
          <input
            type="radio"
            name="response-mode"
            value="stream"
            checked
            class="mr-2"
          />
          <span>流式响应 (实时打字效果)</span>
        </label>
        <label class="flex items-center cursor-pointer">
          <input
            type="radio"
            name="response-mode"
            value="normal"
            class="mr-2"
          />
          <span>普通响应 (一次性显示)</span>
        </label>
      </div>
    </div>

    <footer class="text-darkText py-4">
      <div class="container mx-auto px-4 text-center">
        <p>© 2023 马来西亚股票AI分析神器 |</p>
      </div>
    </footer>

    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const chatContainer = document.getElementById("chat-container");
        const messagesContainer = document.getElementById("messages");
        const stockCodeInput = document.getElementById("stock-code");
        const exchangeIdentifierInput = document.getElementById(
          "exchange-identifier"
        );
        const userInput = document.getElementById("user-input");
        const sendBtn = document.getElementById("send-btn");
        const responseMode = document.getElementsByName("response-mode");

        // 设置预设问题点击事件
        document.querySelectorAll(".preset-btn").forEach((btn) => {
          btn.addEventListener("click", () => {
            const question = btn.getAttribute("data-question");
            userInput.value = question;
            userInput.focus();
          });
        });

        // API 配置
        const API_ENDPOINT = "https://au.dayinbz.cn/index/Aiticker/analyze";

        // 发送消息
        sendBtn.addEventListener("click", sendMessage);
        userInput.addEventListener("keydown", (e) => {
          if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
          }
        });

        // 发送消息到 API
        function sendMessage() {
          const stockCode = stockCodeInput.value.trim();
          const message = userInput.value.trim();
          const exchangeIdentifier = exchangeIdentifierInput.value.trim();
          if (!stockCode) {
            addMessage("error", "请输入股票代码");
            return;
          }

          if (!message) {
            addMessage("error", "请输入您的问题");
            return;
          }

          // 清空输入框
          stockCodeInput.value = "";
          userInput.value = "";

          // 添加用户消息到对话
          const fullMessage = `交易所：${exchangeIdentifier} 股票代码: ${stockCode}\n问题: ${message}`;
          addMessage("user", fullMessage);

          // 获取选中的响应模式
          const mode = Array.from(responseMode).find((r) => r.checked).value;

          // 显示加载状态
          const loadingId = "loading-" + Date.now();
          addLoadingIndicator(loadingId);

          // 调用 API
          if (mode === "stream") {
            sendStreamRequest(
              exchangeIdentifier,
              stockCode,
              message,
              loadingId
            );
          } else {
            sendNormalRequest(
              exchangeIdentifier,
              stockCode,
              message,
              loadingId
            );
          }
        }

        // 发送普通请求
        function sendNormalRequest(
          exchangeIdentifier,
          stockCode,
          message,
          loadingId
        ) {
          fetch(API_ENDPOINT, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              stockCode: stockCode,
              message: message,
              exchangeIdentifier: exchangeIdentifier,
              stream: false,
            }),
          })
            .then((response) => response.json())
            .then((data) => {
              // 移除加载状态
              removeLoadingIndicator(loadingId);
              // 添加 AI 回复
              addMessage("assistant", data.content);
            })
            .catch((error) => {
              removeLoadingIndicator(loadingId);
              addMessage("error", "请求出错: " + error.message);
            });
        }

        // 发送流式请求
        function sendStreamRequest(
          exchangeIdentifier,
          stockCode,
          message,
          loadingId
        ) {
          fetch(API_ENDPOINT, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              stockCode: stockCode,
              message: message,
              exchangeIdentifier: exchangeIdentifier,
              stream: true,
            }),
          })
            .then((response) => {
              if (!response.ok) {
                throw new Error("请求失败: " + response.status);
              }

              // 移除加载状态
              removeLoadingIndicator(loadingId);

              // 创建 AI 回复元素
              const replyId = "reply-" + Date.now();
              addMessage("assistant", "", replyId);
              const replyElement = document.getElementById(replyId);

              // 处理SSE格式的流式响应
              const reader = response.body.getReader();
              const decoder = new TextDecoder();
              let fullText = "";
              let buffer = "";

              function readStream() {
                reader
                  .read()
                  .then(({ done, value }) => {
                    if (done) {
                      // 移除打字光标
                      const cursor =
                        replyElement.querySelector(".typing-cursor");
                      if (cursor) cursor.remove();
                      return;
                    }

                    // 解码并追加到缓冲区
                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;

                    // 处理SSE格式的数据
                    const sseRegex = /^data: (.*)$/gm;
                    let match;

                    while ((match = sseRegex.exec(buffer)) !== null) {
                      const dataLine = match[1];

                      // 忽略[DONE]消息
                      if (dataLine === "[DONE]") {
                        continue;
                      }

                      try {
                        const data = JSON.parse(dataLine);
                        if (
                          data.choices &&
                          data.choices[0] &&
                          data.choices[0].delta
                        ) {
                          const content = data.choices[0].delta.content;
                          if (content) {
                            fullText += content;
                            // 处理换行符
                            fullText = fullText
                              .replace(/\r\n/g, "<br>")
                              .replace(/\r/g, "<br>")
                              .replace(/\n/g, "<br>");
                            replyElement.innerHTML =
                              fullText + '<span class="typing-cursor">|</span>';
                            chatContainer.scrollTop =
                              chatContainer.scrollHeight;
                            // 使用 Promise 实现延迟
                            new Promise((resolve) => setTimeout(resolve, 50));
                          }
                        }
                      } catch (e) {
                        console.error(
                          "解析SSE数据失败:",
                          e,
                          "，数据行:",
                          dataLine
                        );
                      }
                    }

                    // 移除已处理的数据
                    buffer = buffer.substring(sseRegex.lastIndex);

                    // 继续读取流
                    readStream();
                  })
                  .catch((error) => {
                    console.error("读取流失败:", error);
                    replyElement.innerHTML = "读取流失败: " + error.message;
                  });
              }

              readStream();
            })
            .catch((error) => {
              removeLoadingIndicator(loadingId);
              console.log("错误", error);
              addMessage("error", "请求出错: " + error);
            });
        }

        // 添加消息到对话
        function addMessage(role, content, messageId = null) {
          let icon, bgColor, messageClass;

          if (role === "user") {
            icon = '<i class="fa fa-user"></i>';
            bgColor = "user-message"; // 使用新的用户消息样式类
            messageClass = "ml-auto";
          } else if (role === "assistant") {
            icon = '<i class="fa fa-line-chart"></i>';
            bgColor = "assistant-message"; // 使用新的助手消息样式类
            messageClass = "";
          } else {
            icon = '<i class="fa fa-exclamation-triangle"></i>';
            bgColor = "bg-red-100";
            messageClass = "";
          }

          const messageDiv = document.createElement("div");
          messageDiv.className = `flex items-start mb-4 ${
            role === "user" ? "justify-end" : ""
          } message-enter`;

          if (role !== "user") {
            const iconDiv = document.createElement("div");
            iconDiv.className = `${
              role === "assistant" ? "bg-primary" : "bg-red-500"
            } text-white rounded-full p-2 mr-3`;
            iconDiv.innerHTML = icon;
            messageDiv.appendChild(iconDiv);
          }

          const contentDiv = document.createElement("div");
          // 处理换行符
          content = content
            .replace(/\r\n/g, "<br>")
            .replace(/\r/g, "<br>")
            .replace(/\n/g, "<br>");
          contentDiv.className = `${bgColor} ${
            role === "error" ? "text-red-400" : "text-darkText"
          } ${messageClass}`;

          if (messageId) {
            contentDiv.id = messageId;
          }

          contentDiv.innerHTML = content;

          messageDiv.appendChild(contentDiv);
          messagesContainer.appendChild(messageDiv);

          // 添加动画类
          setTimeout(() => {
            messageDiv.classList.add("message-enter-active");
          }, 10);

          chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // 添加加载指示器
        function addLoadingIndicator(id) {
          const loadingDiv = document.createElement("div");
          loadingDiv.className = "flex items-start mb-4 message-enter";
          loadingDiv.id = id;

          const iconDiv = document.createElement("div");
          iconDiv.className = "bg-primary text-white rounded-full p-2 mr-3";
          iconDiv.innerHTML = '<i class="fa fa-line-chart"></i>';

          const contentDiv = document.createElement("div");
          contentDiv.className =
            "bg-neutral text-darkText p-3 rounded-lg max-w-[90%]";
          contentDiv.innerHTML = '<div class="animate-pulse">分析中...</div>';

          loadingDiv.appendChild(iconDiv);
          loadingDiv.appendChild(contentDiv);
          messagesContainer.appendChild(loadingDiv);

          // 添加动画类
          setTimeout(() => {
            loadingDiv.classList.add("message-enter-active");
          }, 10);

          chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // 移除加载指示器
        function removeLoadingIndicator(id) {
          const loadingElement = document.getElementById(id);
          if (loadingElement) {
            loadingElement.classList.remove("message-enter-active");
            setTimeout(() => {
              loadingElement.remove();
            }, 300);
          }
        }
      });
    </script>
  </body>
</html>
