import { Typography, Spin } from "antd";
import styles from "./style.module.less";
import { useState } from "react";

const { Title } = Typography;

export default function AiStockAnalysis() {
  const [loading, setLoading] = useState(true);

  const handleIframeLoad = () => {
    setLoading(false);
  };

  return (
    <div className={styles.container}>
      <Title level={2}>AI Stock Analysis</Title>
      <div className={styles.iframeContainer}>
        {loading && (
          <div className={styles.spinContainer}>
            <Spin size="large" tip="Loading..." />
          </div>
        )}
        <iframe
          className={`${styles.iframe} ${loading ? styles.loading : ""}`}
          src="./aig.html"
          width="100%"
          height="100%"
          onLoad={handleIframeLoad}
        ></iframe>
      </div>
    </div>
  );
}
