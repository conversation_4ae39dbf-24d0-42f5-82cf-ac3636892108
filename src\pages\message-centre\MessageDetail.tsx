import React from "react";
import styles from "./style.module.less";
import dayjs from "dayjs";
import PageHeader from "../../components/PageHeader";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import type { RootState } from "../../store";
import type { MessageCentreData } from "../../types/MessageCentre";
import useMobile from "../../hooks/useMobile";

const MessageDetail: React.FC<{ message?: MessageCentreData | null }> = ({
  message,
}) => {
  const isMobileDetail = useMobile();
  const navigate = useNavigate();
  const { messageDetail } = useSelector((state: RootState) => state.message);
  const detail = isMobileDetail ? messageDetail : message;

  return (
    <div className={styles.messageCentrePage}>
      {isMobileDetail && <PageHeader onBack={() => navigate(-1)} />}

      <div className={styles.messageDetail}>
        {detail && (
          <>
            <div className={styles.detailHeader}>
              <div className={styles.detailDate}>
                {dayjs(detail.create_data).format("D MMMM YYYY HH:mm A")}
              </div>
              <div className={styles.detailTitle}>{detail.event_title}</div>
            </div>
            <div className={styles.detailContent}>{detail.event_msg}</div>
          </>
        )}
      </div>
    </div>
  );
};

export default MessageDetail;
